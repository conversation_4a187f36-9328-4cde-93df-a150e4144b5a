2025-06-11 15:11:21,307 INFO: 应用启动 - PID: 10776 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 15:13:54,584 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:405]
2025-06-11 15:14:11,094 ERROR: 获取应付账款账龄分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ap.id,
                ap.payable_number,
                s.name as supplier_name,
                ap.original_amount,
                ap.paid_amount,
                ap.balance_amount,
                ap.due_date,
                si.stock_in_date,
                si.stock_in_number,
                DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), ?) as aging_days,
                CASE
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), ?) <= 30 THEN '30天以内'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), ?) <= 60 THEN '31-60天'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), ?) <= 90 THEN '61-90天'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), ?) <= 180 THEN '91-180天'
                    ELSE '180天以上'
                END as aging_group
            FROM account_payables ap
            INNER JOIN suppliers s ON ap.supplier_id = s.id
            INNER JOIN stock_ins si ON ap.stock_in_id = si.id
            WHERE ap.area_id = ?
            AND ap.balance_amount > 0
         ORDER BY aging_days DESC, ap.balance_amount DESC]
[parameters: (datetime.date(2025, 6, 11), datetime.date(2025, 6, 11), datetime.date(2025, 6, 11), datetime.date(2025, 6, 11), datetime.date(2025, 6, 11), 42)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:500]
