<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代网页配色方案展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }
        
        body {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 40px 20px;
            margin-bottom: 30px;
        }
        
        h1 {
            font-size: 3.2rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 800;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #6c757d;
            max-width: 700px;
            margin: 0 auto 30px;
        }
        
        .color-schemes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .scheme {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
            transition: transform 0.3s;
            cursor: pointer;
        }
        
        .scheme:hover {
            transform: translateY(-10px);
        }
        
        .scheme.active {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            z-index: 10;
        }
        
        .scheme-header {
            height: 120px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .scheme-name {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 2;
        }
        
        .scheme-colors {
            display: flex;
            height: 60px;
        }
        
        .scheme-color {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.85rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        .scheme-details {
            padding: 20px;
        }
        
        .scheme-details h3 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .scheme-details ul {
            list-style: none;
            padding-left: 5px;
        }
        
        .scheme-details li {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .preview-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            margin-bottom: 40px;
        }
        
        .preview-section h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
            color: #343a40;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .website-preview {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .preview-header {
            height: 50px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
        }
        
        .preview-logo {
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .preview-nav {
            display: flex;
            gap: 20px;
        }
        
        .preview-main {
            flex: 1;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .preview-hero {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        
        .preview-hero h3 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .preview-hero p {
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 50px;
            display: inline-block;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            border: none;
            font-size: 1rem;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: white;
        }
        
        .btn-secondary {
            background: transparent;
            border: 2px solid;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .feature {
            padding: 15px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .feature-text h4 {
            margin-bottom: 5px;
        }
        
        .preview-footer {
            padding: 20px;
            text-align: center;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .mobile-preview {
            background: #f8f9fa;
            border-radius: 25px;
            padding: 15px;
            height: 500px;
            width: 280px;
            margin: 0 auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        
        .mobile-header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .mobile-header::before {
            content: "";
            position: absolute;
            width: 40%;
            height: 5px;
            background: #dee2e6;
            border-radius: 10px;
            bottom: 10px;
        }
        
        .mobile-nav {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
        }
        
        .nav-icon {
            font-size: 1.3rem;
        }
        
        .mobile-content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        .mobile-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .card-title {
            font-weight: 600;
        }
        
        .card-content {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.5;
        }
        
        .instructions {
            text-align: center;
            padding: 20px;
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        /* Color Scheme 1: 深海科技蓝 */
        .scheme-1 .scheme-header {
            background: linear-gradient(135deg, #2563EB, #8B5CF6);
        }
        
        .scheme-1 .color-1 { background: #2563EB; }
        .scheme-1 .color-2 { background: #8B5CF6; }
        .scheme-1 .color-3 { background: #06B6D4; }
        .scheme-1 .color-4 { background: #10B981; }
        
        /* Color Scheme 2: 柔光莫兰迪 */
        .scheme-2 .scheme-header {
            background: linear-gradient(135deg, #6D28D9, #EC4899);
        }
        
        .scheme-2 .color-1 { background: #6D28D9; }
        .scheme-2 .color-2 { background: #EC4899; }
        .scheme-2 .color-3 { background: #F59E0B; }
        .scheme-2 .color-4 { background: #60A5FA; }
        
        /* Color Scheme 3: 极简晨曦 */
        .scheme-3 .scheme-header {
            background: linear-gradient(135deg, #F97316, #EF4444);
        }
        
        .scheme-3 .color-1 { background: #F97316; }
        .scheme-3 .color-2 { background: #EF4444; }
        .scheme-3 .color-3 { background: #3B82F6; }
        .scheme-3 .color-4 { background: #22C55E; }
        
        /* Color Scheme 4: 暗夜霓虹 */
        .scheme-4 .scheme-header {
            background: linear-gradient(135deg, #8B5CF6, #F43F5E);
        }
        
        .scheme-4 .color-1 { background: #8B5CF6; }
        .scheme-4 .color-2 { background: #F43F5E; }
        .scheme-4 .color-3 { background: #06B6D4; }
        .scheme-4 .color-4 { background: #F59E0B; }
        
        /* Color Scheme 5: 自然生态绿 */
        .scheme-5 .scheme-header {
            background: linear-gradient(135deg, #10B981, #0EA5E9);
        }
        
        .scheme-5 .color-1 { background: #10B981; }
        .scheme-5 .color-2 { background: #0EA5E9; }
        .scheme-5 .color-3 { background: #F59E0B; }
        .scheme-5 .color-4 { background: #F97316; }
        
        /* Responsive design */
        @media (max-width: 900px) {
            .preview-container {
                grid-template-columns: 1fr;
            }
            
            .color-schemes {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }
            
            h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 600px) {
            .color-schemes {
                grid-template-columns: 1fr;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .website-preview {
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>现代网页配色方案</h1>
            <p class="subtitle">精心设计的五种现代网页配色方案，融合了渐变效果、柔光色调和深色模式兼容性，适用于各种类型的网站项目。</p>
        </header>
        
        <div class="instructions">
            <p><i class="fas fa-mouse-pointer"></i> 点击下方配色方案卡片查看预览效果</p>
        </div>
        
        <div class="color-schemes">
            <!-- 深海科技蓝 -->
            <div class="scheme scheme-1 active" data-scheme="1">
                <div class="scheme-header">
                    <div class="scheme-name">深海科技蓝</div>
                </div>
                <div class="scheme-colors">
                    <div class="scheme-color color-1">#2563EB</div>
                    <div class="scheme-color color-2">#8B5CF6</div>
                    <div class="scheme-color color-3">#06B6D4</div>
                    <div class="scheme-color color-4">#10B981</div>
                </div>
                <div class="scheme-details">
                    <h3><i class="fas fa-laptop-code"></i> 方案特点</h3>
                    <ul>
                        <li><i class="fas fa-check-circle"></i> 冷色调渐变设计</li>
                        <li><i class="fas fa-check-circle"></i> 科技感与现代感</li>
                        <li><i class="fas fa-check-circle"></i> SaaS/数据类网站首选</li>
                    </ul>
                </div>
            </div>
            
            <!-- 柔光莫兰迪 -->
            <div class="scheme scheme-2" data-scheme="2">
                <div class="scheme-header">
                    <div class="scheme-name">柔光莫兰迪</div>
                </div>
                <div class="scheme-colors">
                    <div class="scheme-color color-1">#6D28D9</div>
                    <div class="scheme-color color-2">#EC4899</div>
                    <div class="scheme-color color-3">#F59E0B</div>
                    <div class="scheme-color color-4">#60A5FA</div>
                </div>
                <div class="scheme-details">
                    <h3><i class="fas fa-palette"></i> 方案特点</h3>
                    <ul>
                        <li><i class="fas fa-check-circle"></i> 低饱和度撞色</li>
                        <li><i class="fas fa-check-circle"></i> 柔和高级感</li>
                        <li><i class="fas fa-check-circle"></i> 时尚创意行业</li>
                    </ul>
                </div>
            </div>
            
            <!-- 极简晨曦 -->
            <div class="scheme scheme-3" data-scheme="3">
                <div class="scheme-header">
                    <div class="scheme-name">极简晨曦</div>
                </div>
                <div class="scheme-colors">
                    <div class="scheme-color color-1">#F97316</div>
                    <div class="scheme-color color-2">#EF4444</div>
                    <div class="scheme-color color-3">#3B82F6</div>
                    <div class="scheme-color color-4">#22C55E</div>
                </div>
                <div class="scheme-details">
                    <h3><i class="fas fa-sun"></i> 方案特点</h3>
                    <ul>
                        <li><i class="fas fa-check-circle"></i> 高对比暖色</li>
                        <li><i class="fas fa-check-circle"></i> 视觉冲击力强</li>
                        <li><i class="fas fa-check-circle"></i> 电商/活动页面</li>
                    </ul>
                </div>
            </div>
            
            <!-- 暗夜霓虹 -->
            <div class="scheme scheme-4" data-scheme="4">
                <div class="scheme-header">
                    <div class="scheme-name">暗夜霓虹</div>
                </div>
                <div class="scheme-colors">
                    <div class="scheme-color color-1">#8B5CF6</div>
                    <div class="scheme-color color-2">#F43F5E</div>
                    <div class="scheme-color color-3">#06B6D4</div>
                    <div class="scheme-color color-4">#F59E0B</div>
                </div>
                <div class="scheme-details">
                    <h3><i class="fas fa-moon"></i> 方案特点</h3>
                    <ul>
                        <li><i class="fas fa-check-circle"></i> 深色背景+亮色</li>
                        <li><i class="fas fa-check-circle"></i> 赛博朋克风格</li>
                        <li><i class="fas fa-check-circle"></i> 游戏/元宇宙主题</li>
                    </ul>
                </div>
            </div>
            
            <!-- 自然生态绿 -->
            <div class="scheme scheme-5" data-scheme="5">
                <div class="scheme-header">
                    <div class="scheme-name">自然生态绿</div>
                </div>
                <div class="scheme-colors">
                    <div class="scheme-color color-1">#10B981</div>
                    <div class="scheme-color color-2">#0EA5E9</div>
                    <div class="scheme-color color-3">#F59E0B</div>
                    <div class="scheme-color color-4">#F97316</div>
                </div>
                <div class="scheme-details">
                    <h3><i class="fas fa-leaf"></i> 方案特点</h3>
                    <ul>
                        <li><i class="fas fa-check-circle"></i> 自然清新色调</li>
                        <li><i class="fas fa-check-circle"></i> 治愈系风格</li>
                        <li><i class="fas fa-check-circle"></i> 环保健康类网站</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2>网页效果预览</h2>
            <div class="preview-container">
                <div class="website-preview">
                    <div class="preview-header">
                        <div class="preview-logo">TechVision</div>
                        <div class="preview-nav">
                            <a href="#">首页</a>
                            <a href="#">产品</a>
                            <a href="#">服务</a>
                            <a href="#">关于</a>
                            <a href="#">联系</a>
                        </div>
                    </div>
                    <div class="preview-main">
                        <div class="preview-hero">
                            <h3>创新科技解决方案</h3>
                            <p>为企业提供前沿的数字化转型工具与咨询服务，助力业务增长</p>
                            <div class="btn btn-primary">了解更多</div>
                        </div>
                        <div class="features">
                            <div class="feature">
                                <div class="feature-icon">
                                    <i class="fas fa-cloud"></i>
                                </div>
                                <div class="feature-text">
                                    <h4>云端部署</h4>
                                    <p>安全可靠的云服务架构</p>
                                </div>
                            </div>
                            <div class="feature">
                                <div class="feature-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="feature-text">
                                    <h4>数据分析</h4>
                                    <p>实时数据洞察与可视化</p>
                                </div>
                            </div>
                            <div class="feature">
                                <div class="feature-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <div class="feature-text">
                                    <h4>企业级安全</h4>
                                    <p>端到端加密与权限管理</p>
                                </div>
                            </div>
                            <div class="feature">
                                <div class="feature-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="feature-text">
                                    <h4>移动适配</h4>
                                    <p>全平台响应式设计</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="preview-footer">
                        © 2023 TechVision 科技有限公司 | 隐私政策 | 使用条款
                    </div>
                </div>
                
                <div class="mobile-preview">
                    <div class="mobile-header">
                        <div class="mobile-logo">AppName</div>
                    </div>
                    <div class="mobile-content">
                        <div class="mobile-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="card-title">个人中心</div>
                            </div>
                            <div class="card-content">
                                管理您的账户信息、设置偏好和安全选项
                            </div>
                        </div>
                        
                        <div class="mobile-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="card-title">通知</div>
                            </div>
                            <div class="card-content">
                                查看最新消息和系统通知
                            </div>
                        </div>
                        
                        <div class="mobile-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="card-title">支付</div>
                            </div>
                            <div class="card-content">
                                管理支付方式，查看交易记录
                            </div>
                        </div>
                        
                        <div class="mobile-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="card-title">设置</div>
                            </div>
                            <div class="card-content">
                                自定义应用偏好和隐私设置
                            </div>
                        </div>
                    </div>
                    <div class="mobile-nav">
                        <div class="nav-item">
                            <div class="nav-icon"><i class="fas fa-home"></i></div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"><i class="fas fa-search"></i></div>
                            <div>搜索</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
                            <div>购物车</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"><i class="fas fa-user"></i></div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const schemes = document.querySelectorAll('.scheme');
            const desktopPreview = document.querySelector('.website-preview');
            const mobilePreview = document.querySelector('.mobile-preview');
            
            // 初始应用深海科技蓝方案
            applyColorScheme(1);
            
            schemes.forEach(scheme => {
                scheme.addEventListener('click', function() {
                    // 移除所有active类
                    schemes.forEach(s => s.classList.remove('active'));
                    // 添加active类到当前点击的方案
                    this.classList.add('active');
                    
                    const schemeId = this.getAttribute('data-scheme');
                    applyColorScheme(schemeId);
                });
            });
            
            function applyColorScheme(schemeId) {
                let primary, secondary, accent, background, text;
                
                switch(schemeId) {
                    case '1': // 深海科技蓝
                        primary = '#2563EB';
                        secondary = '#8B5CF6';
                        accent = '#06B6D4';
                        background = '#F8FAFC';
                        text = '#1E293B';
                        break;
                    case '2': // 柔光莫兰迪
                        primary = '#6D28D9';
                        secondary = '#EC4899';
                        accent = '#F59E0B';
                        background = '#FDF2F8';
                        text = '#334155';
                        break;
                    case '3': // 极简晨曦
                        primary = '#F97316';
                        secondary = '#EF4444';
                        accent = '#3B82F6';
                        background = '#FFFFFF';
                        text = '#1F2937';
                        break;
                    case '4': // 暗夜霓虹
                        primary = '#8B5CF6';
                        secondary = '#F43F5E';
                        accent = '#06B6D4';
                        background = '#0F172A';
                        text = '#E2E8F0';
                        break;
                    case '5': // 自然生态绿
                        primary = '#10B981';
                        secondary = '#0EA5E9';
                        accent = '#F59E0B';
                        background = '#ECFDF5';
                        text = '#1E293B';
                        break;
                }
                
                // 应用颜色到桌面预览
                desktopPreview.style.backgroundColor = background;
                desktopPreview.style.color = text;
                desktopPreview.querySelector('.preview-header').style.backgroundColor = primary;
                desktopPreview.querySelector('.preview-hero').style.background = `linear-gradient(135deg, ${primary}, ${secondary})`;
                desktopPreview.querySelector('.btn-primary').style.background = `linear-gradient(135deg, ${primary}, ${secondary})`;
                
                const features = desktopPreview.querySelectorAll('.feature');
                features[0].querySelector('.feature-icon').style.backgroundColor = primary;
                features[1].querySelector('.feature-icon').style.backgroundColor = secondary;
                features[2].querySelector('.feature-icon').style.backgroundColor = accent;
                features[3].querySelector('.feature-icon').style.backgroundColor = schemeId === '4' ? '#F59E0B' : '#22C55E';
                
                // 应用颜色到移动预览
                mobilePreview.style.backgroundColor = background;
                mobilePreview.style.color = text;
                mobilePreview.querySelector('.mobile-header').style.backgroundColor = primary;
                mobilePreview.querySelector('.mobile-nav').style.backgroundColor = schemeId === '4' ? '#1E293B' : '#FFFFFF';
                
                const cards = mobilePreview.querySelectorAll('.mobile-card');
                cards.forEach(card => {
                    card.style.backgroundColor = schemeId === '4' ? '#1E293B' : '#FFFFFF';
                });
                
                const cardIcons = mobilePreview.querySelectorAll('.card-icon');
                cardIcons[0].style.backgroundColor = primary;
                cardIcons[1].style.backgroundColor = secondary;
                cardIcons[2].style.backgroundColor = accent;
                cardIcons[3].style.backgroundColor = schemeId === '4' ? '#F59E0B' : '#22C55E';
            }
        });
    </script>
</body>
</html>