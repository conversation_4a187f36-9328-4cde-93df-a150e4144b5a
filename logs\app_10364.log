2025-06-11 14:53:07,227 INFO: 应用启动 - PID: 10364 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 14:53:31,231 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:405]
