2025-06-11 15:24:00,986 INFO: 应用启动 - PID: 9784 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 15:26:59,390 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 46900.0, 明细: 五花肉(700.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:26:59,391 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 60300.0, 明细: 白菜(900.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:26:59,408 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611006, 入库单=RK20250525203133, 供应商=优质肉类有限公司, 总金额=107200.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1592]
2025-06-11 15:27:04,497 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 25000.0, 明细: 五花肉(500.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:04,501 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611007, 入库单=RK20250526165105, 供应商=岳阳市绿色蔬菜配送中心, 总金额=25000.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1592]
2025-06-11 15:27:08,078 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 25000.0, 明细: 五花肉(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:08,079 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 50000.0, 明细: 白菜(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:08,083 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611008, 入库单=RK20250528133013, 供应商=调味品产品公司, 总金额=75000.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1592]
2025-06-11 15:27:10,675 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 6000.0, 明细: 淀粉(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:10,676 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 280000.0, 明细: 香肠(1000.0公斤)、鸡肉(1000.0公斤)、牛排(1000.0公斤)、鸡胸肉(1000.0公斤)、猪肉(1000.0公斤)、五花肉(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:10,681 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611009, 入库单=RK20250528223445, 供应商=调味品产品公司, 总金额=286000.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1592]
2025-06-11 15:27:13,248 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 335.0, 明细: 猪肉(10.0千克)、牛肉(15.0千克)、鸡肉(20.0千克) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1539]
2025-06-11 15:27:13,255 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611010, 入库单=RK20250601021012, 供应商=调味品产品公司, 总金额=335.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1592]
2025-06-11 15:33:53,740 ERROR: 获取资产负债表数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            WITH subject_balances AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id,
                    ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) as balance
                FROM accounting_subjects s
                LEFT JOIN voucher_details vd ON s.id = vd.subject_id
                LEFT JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE s.area_id = ?
                AND s.is_active = 1
                AND (fv.voucher_date IS NULL OR fv.voucher_date <= ?)
                AND (fv.status IS NULL OR fv.status IN ('已审核', '已记账'))
                GROUP BY s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id
                HAVING ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) != 0
            )
            SELECT * FROM subject_balances
            WHERE subject_type IN ('资产', '负债', '所有者权益')
            ORDER BY subject_type, code
        ]
[parameters: (42, datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:278]
2025-06-11 15:35:50,698 ERROR: 获取资产负债表数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            WITH subject_balances AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id,
                    ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) as balance
                FROM accounting_subjects s
                LEFT JOIN voucher_details vd ON s.id = vd.subject_id
                LEFT JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE s.area_id = ?
                AND s.is_active = 1
                AND (fv.voucher_date IS NULL OR fv.voucher_date <= ?)
                AND (fv.status IS NULL OR fv.status IN ('已审核', '已记账'))
                GROUP BY s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id
                HAVING ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) != 0
            )
            SELECT * FROM subject_balances
            WHERE subject_type IN ('资产', '负债', '所有者权益')
            ORDER BY subject_type, code
        ]
[parameters: (42, datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:278]
