2025-06-13 21:35:53,362 INFO: 应用启动 - PID: 11060 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-13 21:41:23,931 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 21:41:23,965 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 21:54:01,086 INFO: 批次编辑器保存 - 入库单ID: 95 [in C:\StudentsCMSSP\app\routes\stock_in.py:921]
2025-06-13 21:54:01,096 INFO: 选中的项目: ['209', '210', '211', '212', '213', '214', '215', '216', '217', '218', '219', '220', '221', '222', '223', '224', '225', '226', '227', '228', '229'] [in C:\StudentsCMSSP\app\routes\stock_in.py:922]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_209: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_210: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_211: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_212: 45 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_213: 23 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_214: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_215: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_216: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_217: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_218: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_219: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_220: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_221: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_222: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_223: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_224: 8 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_225: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_226: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_227: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_228: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,096 INFO: 单价字段 - unit_price_229: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 21:54:01,104 INFO: 项目 209 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,104 INFO:   - 供应商ID: 29 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,105 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,105 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,105 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,105 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,106 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,120 INFO: 项目 210 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,121 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,136 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,137 INFO:   - 数量: 9.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,138 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,138 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,138 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,150 INFO: 项目 211 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,151 INFO:   - 供应商ID: 32 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,151 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,151 INFO:   - 数量: 60.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,151 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,152 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,152 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,159 INFO: 项目 212 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,159 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,159 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,159 INFO:   - 数量: 500.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,160 INFO:   - 单价: 45 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,160 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,160 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,166 INFO: 项目 213 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,166 INFO:   - 供应商ID: 31 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,166 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,166 INFO:   - 数量: 450.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,166 INFO:   - 单价: 23 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,166 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,166 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,166 INFO: 项目 214 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,166 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,166 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,166 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,166 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,166 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,166 INFO:   - 过期日期: 2025-06-27 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,185 INFO: 项目 215 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,194 INFO:   - 供应商ID: 34 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,195 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,195 INFO:   - 数量: 60.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,195 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,195 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,195 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,198 INFO: 项目 216 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,199 INFO:   - 供应商ID: 33 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,199 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,210 INFO:   - 数量: 245.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,210 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,211 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,212 INFO:   - 过期日期: 2025-06-20 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,220 INFO: 项目 217 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,220 INFO:   - 供应商ID: 34 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,220 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,221 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,221 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,221 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,221 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,224 INFO: 项目 218 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,224 INFO:   - 供应商ID: 32 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,225 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,225 INFO:   - 数量: 500.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,225 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,225 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,226 INFO:   - 过期日期: 2025-06-20 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,228 INFO: 项目 219 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,228 INFO:   - 供应商ID: 33 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,232 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,233 INFO:   - 数量: 5.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,237 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,246 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,247 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,253 INFO: 项目 220 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,254 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,254 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,254 INFO:   - 数量: 3.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,254 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,254 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,255 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,257 INFO: 项目 221 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,257 INFO:   - 供应商ID: 34 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,257 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,257 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,258 INFO:   - 单价: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,258 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,258 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,271 INFO: 项目 222 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,271 INFO:   - 供应商ID: 33 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,272 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,272 INFO:   - 数量: 24.3 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,273 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,273 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,274 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,280 INFO: 项目 223 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,280 INFO:   - 供应商ID: 37 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,281 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,281 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,281 INFO:   - 单价: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,281 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,282 INFO:   - 过期日期: 2025-12-10 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,284 INFO: 项目 224 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,284 INFO:   - 供应商ID: 37 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,285 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,285 INFO:   - 数量: 47.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,285 INFO:   - 单价: 8 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,285 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,285 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,294 INFO: 项目 225 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,297 INFO:   - 供应商ID: 37 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,300 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,301 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,302 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,304 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,305 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,315 INFO: 项目 226 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,315 INFO:   - 供应商ID: 37 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,315 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,316 INFO:   - 数量: 186.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,316 INFO:   - 单价: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,316 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,316 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,319 INFO: 项目 227 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,319 INFO:   - 供应商ID: 33 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,319 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,320 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,320 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,321 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,321 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,327 INFO: 项目 228 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,329 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,330 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,332 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,332 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,333 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,333 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:54:01,338 INFO: 项目 229 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 21:54:01,339 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 21:54:01,340 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 21:54:01,340 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 21:54:01,341 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 21:54:01,341 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 21:54:01,341 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 21:55:01,758 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 21:55:12,545 INFO: 库存统计页面：为区域 [44] 找到 13 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:970]
2025-06-13 21:55:29,824 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 21:55:39,591 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 21:55:39,591 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 21:55:39,591 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 21:55:39,591 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 21:55:55,539 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-09', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 21:55:55,539 INFO: 查询日期: 2025-06-09, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 21:55:55,539 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:55:55,539 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:55:55,555 INFO: 未找到周菜单 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:417]
2025-06-13 21:56:02,516 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-09', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 21:56:02,518 INFO: 查询日期: 2025-06-09, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 21:56:02,518 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:02,519 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:02,520 INFO: 未找到周菜单 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:417]
2025-06-13 21:56:02,520 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:02,521 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:02,522 INFO: 未找到周菜单 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:417]
2025-06-13 21:56:10,683 INFO: 发布周菜单成功: id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-13 21:56:14,770 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 21:56:14,770 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 21:56:14,770 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 21:56:14,770 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 21:56:25,779 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-09', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 21:56:25,779 INFO: 查询日期: 2025-06-09, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 21:56:25,779 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:25,779 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:29,825 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-09', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 21:56:29,825 INFO: 查询日期: 2025-06-09, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 21:56:29,825 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:29,825 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:29,855 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:29,856 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:31,977 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-09', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 21:56:31,977 INFO: 查询日期: 2025-06-09, 区域: 44, 餐次: ['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 21:56:31,985 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:31,985 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:32,000 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:32,000 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:56:32,067 INFO: 查询餐次: 晚餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 21:56:32,067 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 21:59:06,453 INFO: 超级编辑器接收到的数据: [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:202]
2025-06-13 21:59:06,453 INFO:   area_id: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:203]
2025-06-13 21:59:06,453 INFO:   warehouse_id: 6 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:204]
2025-06-13 21:59:06,454 INFO:   consumption_date: 2025-06-09 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-13 21:59:06,454 INFO:   meal_types: ['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-13 21:59:06,454 INFO:   diners_count: 1000 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-13 21:59:06,454 INFO:   notes:  [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-13 21:59:06,455 INFO: 创建包含餐次 早餐+午餐+晚餐 的消耗计划 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:235]
2025-06-13 21:59:06,456 INFO: 日菜单功能已移除，将创建独立的消耗计划: area_id=44, date=2025-06-09, meal_types=['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:239]
2025-06-13 21:59:06,460 INFO: 创建了消耗计划 ID: 36 (餐次: 早餐+午餐+晚餐) [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:261]
2025-06-13 21:59:06,638 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 21:59:06,641 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 21:59:06,643 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 21:59:06,644 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 21:59:21,842 INFO: 使用库存记录: ID=77, 批次号=B20250613c0cca3, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,842 INFO: 准备更新库存: ID=77, 减少数量=100.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,843 INFO: 使用库存记录: ID=78, 批次号=B202506135b4236, 数量=9.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,843 INFO: 准备更新库存: ID=78, 减少数量=7.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,843 WARNING: 库存不足: 食材=鸡蛋, 需要=23.0, 当前=9.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:634]
2025-06-13 21:59:21,845 INFO: 创建平账入库: 食材=鸡蛋, 需要=23.0, 当前=9.0, 差额=14.0, 批次号=BAL-20250613-59-e2299d [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:53]
2025-06-13 21:59:21,860 INFO: 创建新库存记录: 批次号=BAL-20250613-59-e2299d, 数量=14.0 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:170]
2025-06-13 21:59:21,871 INFO: 平账入库完成: 食材=鸡蛋, 补充数量=14.0, 入库单ID=96 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:200]
2025-06-13 21:59:21,873 INFO: 平账入库成功: 食材=鸡蛋, 补充数量=14.0, 批次号=BAL-20250613-59-e2299d [in C:\StudentsCMSSP\app\routes\consumption_plan.py:652]
2025-06-13 21:59:21,883 INFO: 平账入库后找到 1 条库存记录 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:697]
2025-06-13 21:59:21,884 INFO: 使用库存记录: ID=98, 批次号=BAL-20250613-59-e2299d, 数量=14.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,885 INFO: 准备更新库存: ID=98, 减少数量=23.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,885 INFO: 使用库存记录: ID=80, 批次号=B20250613cd85a4, 数量=500.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,887 INFO: 准备更新库存: ID=80, 减少数量=450.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,892 WARNING: 库存不足: 食材=猪肉, 需要=400.0, 当前=100.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:634]
2025-06-13 21:59:21,897 INFO: 创建平账入库: 食材=猪肉, 需要=400.0, 当前=100.0, 差额=300.0, 批次号=BAL-20250613-1-9d3422 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:53]
2025-06-13 21:59:21,907 INFO: 创建新库存记录: 批次号=BAL-20250613-1-9d3422, 数量=300.0 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:170]
2025-06-13 21:59:21,907 INFO: 平账入库完成: 食材=猪肉, 补充数量=300.0, 入库单ID=97 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:200]
2025-06-13 21:59:21,907 INFO: 平账入库成功: 食材=猪肉, 补充数量=300.0, 批次号=BAL-20250613-1-9d3422 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:652]
2025-06-13 21:59:21,917 INFO: 平账入库后找到 1 条库存记录 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:697]
2025-06-13 21:59:21,917 INFO: 使用库存记录: ID=99, 批次号=BAL-20250613-1-9d3422, 数量=300.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,918 INFO: 准备更新库存: ID=99, 减少数量=400.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,918 INFO: 使用库存记录: ID=82, 批次号=B202506131a2997, 数量=20.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,918 INFO: 准备更新库存: ID=82, 减少数量=20.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,918 INFO: 使用库存记录: ID=83, 批次号=B20250613d33fe4, 数量=60.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,918 INFO: 准备更新库存: ID=83, 减少数量=53.99 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,919 INFO: 使用库存记录: ID=84, 批次号=B20250613eb58a9, 数量=245.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,919 INFO: 准备更新库存: ID=84, 减少数量=240.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,919 INFO: 使用库存记录: ID=85, 批次号=B20250613e7eeb7, 数量=20.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,920 INFO: 准备更新库存: ID=85, 减少数量=18.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,920 WARNING: 库存不足: 食材=白菜, 需要=320.0, 当前=245.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:634]
2025-06-13 21:59:21,923 INFO: 创建平账入库: 食材=白菜, 需要=320.0, 当前=245.0, 差额=75.0, 批次号=BAL-20250613-4-ce6539 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:53]
2025-06-13 21:59:21,929 INFO: 创建新库存记录: 批次号=BAL-20250613-4-ce6539, 数量=75.0 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:170]
2025-06-13 21:59:21,937 INFO: 平账入库完成: 食材=白菜, 补充数量=75.0, 入库单ID=98 [in C:\StudentsCMSSP\app\utils\balance_stock_in.py:200]
2025-06-13 21:59:21,944 INFO: 平账入库成功: 食材=白菜, 补充数量=75.0, 批次号=BAL-20250613-4-ce6539 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:652]
2025-06-13 21:59:21,955 INFO: 平账入库后找到 1 条库存记录 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:697]
2025-06-13 21:59:21,956 INFO: 使用库存记录: ID=100, 批次号=BAL-20250613-4-ce6539, 数量=75.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,957 INFO: 准备更新库存: ID=100, 减少数量=320.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,958 INFO: 使用库存记录: ID=87, 批次号=B20250613d1dc3c, 数量=5.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,959 INFO: 准备更新库存: ID=87, 减少数量=1.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,963 INFO: 使用库存记录: ID=88, 批次号=B20250613b9d68a, 数量=3.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,963 INFO: 准备更新库存: ID=88, 减少数量=1.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,964 INFO: 使用库存记录: ID=89, 批次号=B202506137f4d4c, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,964 INFO: 准备更新库存: ID=89, 减少数量=56.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,965 INFO: 使用库存记录: ID=90, 批次号=B20250613e9a55e, 数量=24.30 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,965 INFO: 准备更新库存: ID=90, 减少数量=12.8 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,965 INFO: 使用库存记录: ID=91, 批次号=B202506135019ab, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,966 INFO: 准备更新库存: ID=91, 减少数量=45.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,966 INFO: 使用库存记录: ID=92, 批次号=B20250613971ce6, 数量=47.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,966 INFO: 准备更新库存: ID=92, 减少数量=22.98 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,966 INFO: 使用库存记录: ID=93, 批次号=B20250613b58e68, 数量=20.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,968 INFO: 准备更新库存: ID=93, 减少数量=7.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,968 INFO: 使用库存记录: ID=94, 批次号=B20250613164904, 数量=186.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,968 INFO: 准备更新库存: ID=94, 减少数量=180.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,968 INFO: 使用库存记录: ID=95, 批次号=B20250613d2f160, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,968 INFO: 准备更新库存: ID=95, 减少数量=67.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,969 INFO: 使用库存记录: ID=96, 批次号=B2025061300f043, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,969 INFO: 准备更新库存: ID=96, 减少数量=53.98 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,969 INFO: 使用库存记录: ID=97, 批次号=B20250613425363, 数量=100.00 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:703]
2025-06-13 21:59:21,969 INFO: 准备更新库存: ID=97, 减少数量=89.0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:724]
2025-06-13 21:59:21,984 INFO: 执行库存更新: 共 21 条记录 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:753]
2025-06-13 21:59:22,079 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 21:59:22,079 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 21:59:22,080 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 21:59:22,080 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 21:59:31,551 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 21:59:31,551 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 21:59:31,551 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 21:59:31,551 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 21:59:41,540 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-13 21:59:41,540 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-13 21:59:41,540 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-13 21:59:41,540 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-13 21:59:41,540 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-13 21:59:41,555 INFO: 周菜单 41 总共有 11 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-13 21:59:41,555 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,555 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,555 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,556 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,557 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,557 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 21:59:41,563 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-13 21:59:41,565 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-13 21:59:41,565 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-13 21:59:41,571 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-13 21:59:41,591 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-13 21:59:41,605 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,607 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,607 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,607 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,610 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,612 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,613 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,615 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,616 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,616 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,617 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,619 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,620 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,621 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,622 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,623 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,624 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,625 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,626 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,627 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,628 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 21:59:41,628 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-13 22:00:52,959 INFO: 查询菜谱：日期=2025-06-13, 星期=4(0=周一), day_of_week=5, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-13 22:00:52,961 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-13 22:00:52,963 INFO: 匹配条件的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-13 22:00:52,963 INFO: 未找到 2025-06-13 午餐 的菜谱信息 [in C:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-13 22:00:52,971 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-13 22:04:51,503 INFO: 取消发布周菜单成功: id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:571]
2025-06-13 22:07:00,176 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-13 22:07:00,181 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,182 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,182 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,182 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,183 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,183 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,183 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,184 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,184 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,184 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,184 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 22:07:00,186 INFO: 副表数据映射构建完成: 1 天, 11 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-13 22:07:00,186 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-13 22:07:00,186 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,187 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,187 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,187 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,187 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,187 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,188 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,188 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,188 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,188 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,188 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,189 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,189 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,189 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,189 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,189 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,190 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,190 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,192 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,193 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,194 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-13 22:07:00,198 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-13 22:07:00,199 INFO: 主表数据补全完成，准备保存: 总菜品数=37, 已补全=37, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-13 22:07:00,206 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-13 22:07:00,211 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-13 22:07:00,213 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-13 22:07:00,262 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-13 22:07:00,282 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-13 22:07:05,540 INFO: 发布周菜单成功: id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-13 22:07:05,630 INFO: 更新了日期 2025-06-09 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-13 22:07:05,655 INFO: 更新了日期 2025-06-13 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-13 22:12:23,137 INFO: 批次编辑器保存 - 入库单ID: 99 [in C:\StudentsCMSSP\app\routes\stock_in.py:921]
2025-06-13 22:12:23,137 INFO: 选中的项目: ['233', '234', '235', '236', '237', '238', '239', '240', '241', '247', '249', '250', '251', '252', '253', '254', '255', '256', '257'] [in C:\StudentsCMSSP\app\routes\stock_in.py:922]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_233: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_234: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_235: 45 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_236: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_237: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,137 INFO: 单价字段 - unit_price_238: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_239: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_240: 11 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_241: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_247: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_249: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_250: 5 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_251: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_252: 20 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_253: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_254: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_255: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_256: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 单价字段 - unit_price_257: 7 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-13 22:12:23,152 INFO: 项目 233 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,152 INFO:   - 供应商ID: 31 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,152 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,152 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,152 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,159 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,159 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,170 INFO: 项目 234 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,171 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,177 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,177 INFO:   - 数量: 4.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,178 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,178 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,178 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,195 INFO: 项目 235 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,198 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,198 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,198 INFO:   - 数量: 34.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,199 INFO:   - 单价: 45 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,199 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,199 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,217 INFO: 项目 236 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,219 INFO:   - 供应商ID: 31 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,219 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,220 INFO:   - 数量: 22.97 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,220 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,220 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,221 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,223 INFO: 项目 237 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,223 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,224 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,224 INFO:   - 数量: 30.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,224 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,224 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,224 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,238 INFO: 项目 238 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,244 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,245 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,245 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,246 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,248 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,248 INFO:   - 过期日期: 2025-06-27 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,255 INFO: 项目 239 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,255 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,255 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,255 INFO:   - 数量: 125.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,256 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,256 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,257 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,272 INFO: 项目 240 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,275 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,275 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,275 INFO:   - 数量: 121.7 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,276 INFO:   - 单价: 11 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,279 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,279 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,282 INFO: 项目 241 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,282 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,282 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,283 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,283 INFO:   - 单价: 6 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,283 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,283 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,290 INFO: 项目 247 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,290 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,290 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,290 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,290 INFO:   - 单价: 9 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,291 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,291 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,307 INFO: 项目 249 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,310 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,311 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,311 INFO:   - 数量: 220 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,311 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,312 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,312 INFO:   - 过期日期: 2025-12-10 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,318 INFO: 项目 250 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,319 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,319 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,319 INFO:   - 数量: 23 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,319 INFO:   - 单价: 5 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,320 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,320 INFO:   - 过期日期: 2025-12-10 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,339 INFO: 项目 251 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,341 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,342 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,342 INFO:   - 数量: 47.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,342 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,342 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,343 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,345 INFO: 项目 252 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,346 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,346 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,346 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,346 INFO:   - 单价: 20 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,347 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,347 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,349 INFO: 项目 253 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,349 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,350 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,350 INFO:   - 数量: 96.7 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,350 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,350 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,351 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,353 INFO: 项目 254 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,353 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,353 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,353 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,354 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,354 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,354 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,365 INFO: 项目 255 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,367 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,367 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,368 INFO:   - 数量: 10.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,369 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,369 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,369 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,377 INFO: 项目 256 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,377 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,378 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,378 INFO:   - 数量: 500.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,378 INFO:   - 单价: 12 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,378 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,379 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:12:23,381 INFO: 项目 257 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-13 22:12:23,381 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-13 22:12:23,381 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-13 22:12:23,382 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-13 22:12:23,382 INFO:   - 单价: 7 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-13 22:12:23,382 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-13 22:12:23,382 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-13 22:39:33,340 INFO: 用户 guest_demo 正在执行状态为 已完成 的入库单 97 [in C:\StudentsCMSSP\app\routes\stock_in.py:1396]
2025-06-13 22:40:43,362 ERROR: 查看入库食材详情失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'menu_plans' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT
                cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
                a.name as area_name, a.id as area_id
            FROM
                stock_out_items soi
            JOIN
                stock_outs so ON soi.stock_out_id = so.id
            JOIN
                consumption_plans cp ON so.consumption_plan_id = cp.id
            JOIN
                menu_plans mp ON cp.menu_plan_id = mp.id
            JOIN
                administrative_areas a ON mp.area_id = a.id
            WHERE
                soi.batch_number = ?
            ORDER BY
                cp.consumption_date DESC
        ]
[parameters: ('BAL-20250613-4-ce6539',)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\StudentsCMSSP\app\routes\stock_in_detail.py:124]
2025-06-13 22:50:19,430 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 22:50:19,446 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 22:50:25,546 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 22:50:25,546 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 22:50:25,550 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 22:50:25,550 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 22:50:39,800 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 22:50:39,800 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 22:50:39,806 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 22:50:39,806 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 22:52:54,858 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 22:52:54,901 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 22:52:54,902 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:52:54,902 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:36,480 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 22:57:36,485 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 22:57:36,485 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:36,486 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:40,460 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 22:57:40,463 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 22:57:40,463 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:40,463 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:40,510 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:40,510 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:43,749 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 22:57:43,752 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 22:57:43,752 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:43,752 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:43,785 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:43,786 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:57:43,855 INFO: 查询餐次: 晚餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 22:57:43,855 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 22:59:50,670 INFO: 超级编辑器接收到的数据: [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:202]
2025-06-13 22:59:50,671 INFO:   area_id: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:203]
2025-06-13 22:59:50,671 INFO:   warehouse_id: 6 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:204]
2025-06-13 22:59:50,671 INFO:   consumption_date: 2025-06-13 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-13 22:59:50,672 INFO:   meal_types: ['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-13 22:59:50,672 INFO:   diners_count: 1000 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-13 22:59:50,672 INFO:   notes:  [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-13 22:59:50,675 INFO: 创建包含餐次 早餐+午餐+晚餐 的消耗计划 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:235]
2025-06-13 22:59:50,675 INFO: 日菜单功能已移除，将创建独立的消耗计划: area_id=44, date=2025-06-13, meal_types=['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:239]
2025-06-13 22:59:50,680 INFO: 创建了消耗计划 ID: 37 (餐次: 早餐+午餐+晚餐) [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:261]
2025-06-13 22:59:50,780 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 22:59:50,781 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 22:59:50,783 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 22:59:50,783 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:00:03,435 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:00:03,435 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:00:03,435 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:00:03,435 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:00:09,901 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:00:09,901 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:00:09,901 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:00:09,901 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:00:16,302 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:00:16,302 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:00:16,302 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:00:16,302 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:00:26,181 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:00:26,184 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:00:26,184 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:26,185 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:00:27,422 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:00:27,422 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:00:27,422 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:27,422 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:00:27,460 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:27,460 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:00:27,942 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:00:27,942 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐', '晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:00:27,942 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:27,942 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:00:27,996 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:27,997 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:00:28,066 INFO: 查询餐次: 晚餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:00:28,066 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:06:50,374 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-13 23:06:50,374 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-13 23:06:50,375 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-13 23:06:50,384 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-13 23:06:50,384 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-13 23:06:50,394 INFO: 周菜单 41 总共有 26 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-13 23:06:50,395 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,395 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,395 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,396 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,396 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,396 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,396 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,396 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,397 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,397 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,397 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,397 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,398 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,398 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,398 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,399 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,399 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,399 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,399 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,399 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,400 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,400 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,400 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,400 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,400 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,401 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:06:50,406 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-13 23:06:50,407 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-13 23:06:50,407 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-13 23:06:50,411 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-13 23:06:50,417 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-13 23:06:50,418 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,420 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,420 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,422 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,422 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,424 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,425 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,427 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,428 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,429 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,430 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,431 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,433 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,434 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,435 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,437 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,438 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,440 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,441 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,442 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,444 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:06:50,444 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-13 23:09:56,196 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 23:09:56,348 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 23:10:06,448 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 23:10:12,579 INFO: 库存统计页面：为区域 [44] 找到 13 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:970]
