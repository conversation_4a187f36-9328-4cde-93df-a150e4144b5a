2025-06-14 19:21:09,520 INFO: 应用启动 - PID: 7756 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-14 20:09:52,168 INFO: 取消发布周菜单成功: id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:571]
2025-06-14 20:10:24,702 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-14 20:10:24,716 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,718 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,718 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,719 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,720 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,721 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,721 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,722 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,723 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,724 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,725 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,725 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,726 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,726 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,727 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,728 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,728 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,729 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,730 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,731 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,732 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,733 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,735 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,736 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,737 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,738 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:10:24,738 INFO: 副表数据映射构建完成: 2 天, 26 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-14 20:10:24,738 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:10:24,738 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,738 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,738 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,753 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,753 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,756 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,757 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,759 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,761 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,762 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,763 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,765 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,765 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,765 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,766 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,766 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,766 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,766 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,767 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,767 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,768 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,768 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,769 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,769 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,769 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:10:24,769 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:10:24,769 INFO: 主表数据补全完成，准备保存: 总菜品数=56, 已补全=56, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-14 20:10:24,784 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-14 20:10:24,794 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-14 20:10:24,794 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:10:24,795 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:10:24,879 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-14 20:10:24,879 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-14 20:13:58,009 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-14 20:13:58,018 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,019 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,020 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,021 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,021 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,022 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,023 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,029 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,029 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,030 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,030 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,032 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,033 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,033 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,035 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,036 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,036 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,036 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,036 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,036 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,037 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,037 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,037 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,037 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,038 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:13:58,038 INFO: 副表数据映射构建完成: 3 天, 30 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-14 20:13:58,038 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:13:58,039 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:13:58,039 WARNING: 跳过无效日期: 6 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:13:58,039 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,041 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,041 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,041 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,041 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,041 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,041 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,042 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,042 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,042 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,042 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,042 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,042 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,042 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,043 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,043 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,043 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,043 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,043 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,044 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,044 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,044 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,044 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,045 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,045 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,045 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,046 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,046 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,046 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,046 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,046 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,047 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,047 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,047 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,047 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,047 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,048 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,048 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,048 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,048 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,048 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,048 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,050 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,050 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,051 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,052 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,052 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,052 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,052 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,052 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,052 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,053 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,053 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,053 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:13:58,053 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:13:58,054 INFO: 主表数据补全完成，准备保存: 总菜品数=87, 已补全=87, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-14 20:13:58,077 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-14 20:13:58,108 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-14 20:13:58,109 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:13:58,109 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:13:58,109 WARNING: 跳过无效日期: 6 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:13:58,279 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-14 20:13:58,279 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-14 20:15:02,644 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-14 20:15:02,644 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,644 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,644 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,644 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,657 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,657 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,657 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,658 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,659 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,659 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,660 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,660 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,661 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,661 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,662 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,662 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,662 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,663 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,664 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,664 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,665 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,666 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,666 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,667 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,667 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,667 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,668 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,669 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,669 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,671 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,671 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,671 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,672 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,672 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 湘菜手撕包菜, ID=382 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,672 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,672 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 大碗冬瓜, ID=388 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,672 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,673 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 刨花青笋, ID=390 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,673 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,673 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,673 INFO: 副表数据: 日期=7, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,673 INFO: 副表数据: 日期=7, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,674 INFO: 副表数据: 日期=7, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,674 INFO: 副表数据: 日期=7, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,675 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,675 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,675 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,676 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,676 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,676 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,677 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,677 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,677 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,677 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,677 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,678 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,678 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-14 20:15:02,679 INFO: 副表数据映射构建完成: 4 天, 57 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-14 20:15:02,679 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:15:02,679 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:15:02,680 WARNING: 跳过无效日期: 6 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:15:02,680 WARNING: 跳过无效日期: 7 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-14 20:15:02,680 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,681 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,681 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,681 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,681 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,681 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,682 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,685 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,686 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,687 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,687 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,690 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,691 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,692 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,692 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,692 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,692 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,692 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,693 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,693 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,693 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,693 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,694 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,694 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,694 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,694 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,694 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,695 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,695 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,695 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,695 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,696 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,696 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,696 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,696 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,697 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,697 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,698 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=384 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,698 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,698 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,698 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,699 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,699 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,699 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,699 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,699 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,699 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,700 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 韭菜炒藕丝, ID=378 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,700 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,700 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,700 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,700 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,701 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,701 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,701 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,702 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,702 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,702 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,702 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,702 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,702 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,703 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,703 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,703 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,703 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,703 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,704 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 湘菜手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,704 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 湘菜手撕包菜, ID=382 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,704 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,704 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,704 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 大碗冬瓜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,704 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 大碗冬瓜, ID=388 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,705 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 手撕包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,705 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 手撕包菜, ID=385 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,705 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 刨花青笋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,706 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 刨花青笋, ID=390 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,706 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 大碗长豆角 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,706 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 大碗长豆角, ID=386 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,706 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,707 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,707 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=早餐, 菜品=🏫 包子 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,707 INFO: 从副表补全recipe_id: 日期=7, 餐次=早餐, 菜品=🏫 包子, ID=404 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,707 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=早餐, 菜品=🏫 熟鸡蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,707 INFO: 从副表补全recipe_id: 日期=7, 餐次=早餐, 菜品=🏫 熟鸡蛋, ID=402 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,709 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=早餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,709 INFO: 从副表补全recipe_id: 日期=7, 餐次=早餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,709 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=早餐, 菜品=🏫 爽口面条 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,709 INFO: 从副表补全recipe_id: 日期=7, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,709 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,710 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,710 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,710 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,710 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 韭菜炒豆芽 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,710 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 韭菜炒豆芽, ID=395 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,711 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 炒包菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,711 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 炒包菜, ID=391 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,711 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 黄豆红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,712 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=394 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,712 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,712 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,712 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,712 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,713 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 蒸蛋羹 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,713 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 蒸蛋羹, ID=398 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,713 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 麻婆豆腐 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,713 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 麻婆豆腐, ID=399 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,713 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 豆腐汤 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,714 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=397 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,714 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,714 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=393 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,714 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 红烧肉 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,714 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 红烧肉, ID=400 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,714 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 红薯米饭 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-14 20:15:02,715 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 红薯米饭, ID=392 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-14 20:15:02,715 INFO: 主表数据补全完成，准备保存: 总菜品数=114, 已补全=114, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-14 20:15:02,729 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-14 20:15:02,733 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-14 20:15:02,733 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:15:02,734 WARNING: 跳过无效日期: 5 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:15:02,734 WARNING: 跳过无效日期: 6 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:15:02,734 WARNING: 跳过无效日期: 7 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-14 20:15:03,004 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-14 20:15:03,004 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-14 20:26:44,887 ERROR: 周菜单操作异常: time data 'undefined' does not match format '%Y-%m-%d' [in C:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 130, in plan
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data 'undefined' does not match format '%Y-%m-%d'
2025-06-14 20:27:04,911 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-16"}' [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-14 20:27:04,912 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-16 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-14 20:27:04,912 INFO: 检查用户权限: user_id=38, area_id=44 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-14 20:27:04,913 INFO: 用户角色: ['学校管理员'] [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-14 20:27:04,924 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-14 20:27:04,924 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-16, created_by=38 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-14 20:27:04,926 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-16, created_by=38 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-14 20:27:04,927 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-14 20:27:04,932 INFO: 计算的周结束日期: 2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-14 20:27:04,932 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-14 20:27:04,933 INFO: 获取周菜单: area_id=44, week_start=2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-14 20:27:04,933 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-14 20:27:04,933 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-14 20:27:04,945 INFO: 主查询未找到菜单: area_id=44, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-14 20:27:04,945 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-14 20:27:04,945 INFO: 准备执行SQL创建菜单 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-14 20:27:04,946 INFO: SQL参数: {'area_id': '44', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 38} [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-14 20:27:04,946 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-14 20:27:04,958 INFO: SQL执行成功，获取到ID: 42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-14 20:27:04,959 INFO: 检查数据库连接状态 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-14 20:27:04,960 INFO: 数据库连接正常 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-14 20:27:04,970 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-14 20:27:04,970 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-14 20:27:04,973 INFO: 验证成功: 菜单已创建 ID=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-14 20:27:04,974 INFO: 周菜单创建成功: id=42 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-14 20:27:04,975 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 42, 'status': '计划中'} [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-14 20:28:08,770 INFO: 获取副表数据用于补全主表: weekly_menu_id=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-14 20:28:08,775 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-14 20:28:08,775 INFO: 主表数据补全完成，准备保存: 总菜品数=24, 已补全=24, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-14 20:28:08,777 INFO: 删除现有菜单食谱(主表): weekly_menu_id=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-14 20:28:08,780 INFO: 删除现有菜单食谱(副表): weekly_menu_id=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-14 20:28:08,838 INFO: 保存周菜单成功(主表和副表): id=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-14 20:28:08,838 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-14 20:28:15,436 INFO: 发布周菜单成功: id=42 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-14 20:28:15,480 INFO: 更新了日期 2025-06-16 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-14 20:28:15,491 INFO: 更新了日期 2025-06-17 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-14 20:28:28,763 INFO: 发布周菜单成功: id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-14 20:29:29,833 INFO: 批次编辑器保存 - 入库单ID: 99 [in C:\StudentsCMSSP\app\routes\stock_in.py:921]
2025-06-14 20:29:29,834 INFO: 选中的项目: ['233', '234', '235', '236', '237', '238', '239', '240', '241', '242', '243', '244', '245', '246', '247', '248', '249', '250', '251', '252', '253', '254', '255', '256', '257'] [in C:\StudentsCMSSP\app\routes\stock_in.py:922]
2025-06-14 20:29:29,834 INFO: 单价字段 - unit_price_233: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,834 INFO: 单价字段 - unit_price_234: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,835 INFO: 单价字段 - unit_price_235: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,835 INFO: 单价字段 - unit_price_236: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,835 INFO: 单价字段 - unit_price_237: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,836 INFO: 单价字段 - unit_price_238: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,836 INFO: 单价字段 - unit_price_239: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,836 INFO: 单价字段 - unit_price_240: 11.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,836 INFO: 单价字段 - unit_price_241: 6.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,836 INFO: 单价字段 - unit_price_242: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,837 INFO: 单价字段 - unit_price_243: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,837 INFO: 单价字段 - unit_price_244: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,837 INFO: 单价字段 - unit_price_245: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,837 INFO: 单价字段 - unit_price_246: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,837 INFO: 单价字段 - unit_price_247: 9.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,838 INFO: 单价字段 - unit_price_248: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,838 INFO: 单价字段 - unit_price_249: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,838 INFO: 单价字段 - unit_price_250: 5.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,838 INFO: 单价字段 - unit_price_251: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,838 INFO: 单价字段 - unit_price_252: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,839 INFO: 单价字段 - unit_price_253: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,839 INFO: 单价字段 - unit_price_254: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,839 INFO: 单价字段 - unit_price_255: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,839 INFO: 单价字段 - unit_price_256: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,840 INFO: 单价字段 - unit_price_257: 7.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-14 20:29:29,859 INFO: 项目 233 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,859 INFO:   - 供应商ID: 31 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,860 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,860 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,860 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,860 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,861 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,919 INFO: 项目 234 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,919 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,919 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,920 INFO:   - 数量: 4.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,920 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,920 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,920 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,944 INFO: 项目 235 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,944 INFO:   - 供应商ID: 30 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,944 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,945 INFO:   - 数量: 34.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,945 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,945 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,946 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,955 INFO: 项目 236 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,955 INFO:   - 供应商ID: 31 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,955 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,955 INFO:   - 数量: 22.97 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,956 INFO:   - 单价: 45.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,956 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,956 INFO:   - 过期日期: 2025-06-16 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,965 INFO: 项目 237 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,965 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,965 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,966 INFO:   - 数量: 30.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,966 INFO:   - 单价: 38.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,966 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,966 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,968 INFO: 项目 238 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,969 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,969 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,969 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,969 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,970 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,970 INFO:   - 过期日期: 2025-06-27 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,977 INFO: 项目 239 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,977 INFO:   - 供应商ID: 35 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,977 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,977 INFO:   - 数量: 125.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,978 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,978 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,978 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,980 INFO: 项目 240 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,980 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,981 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,981 INFO:   - 数量: 121.7 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,981 INFO:   - 单价: 11.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,981 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,981 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,983 INFO: 项目 241 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,984 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,984 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,984 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,984 INFO:   - 单价: 6.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,985 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,985 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:29,996 INFO: 项目 242 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:29,996 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:29,997 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:29,997 INFO:   - 数量: 10.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:29,997 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:29,998 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:29,998 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,000 INFO: 项目 243 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,000 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,001 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,001 INFO:   - 数量: 10.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,001 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,001 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,002 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,004 INFO: 项目 244 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,004 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,005 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,005 INFO:   - 数量: 15.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,005 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,005 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,006 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,008 INFO: 项目 245 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,008 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,009 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,009 INFO:   - 数量: 24.3 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,009 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,009 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,009 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,011 INFO: 项目 246 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,012 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,012 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,012 INFO:   - 数量: 121.7 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,012 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,012 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,013 INFO:   - 过期日期: 2025-06-20 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,015 INFO: 项目 247 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,015 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,015 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,015 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,015 INFO:   - 单价: 9.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,016 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,016 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,018 INFO: 项目 248 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,018 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,018 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,018 INFO:   - 数量: 60.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,019 INFO:   - 单价: 8.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,019 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,019 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,021 INFO: 项目 249 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,022 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,022 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,022 INFO:   - 数量: 220.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,022 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,023 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,023 INFO:   - 过期日期: 2025-12-10 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,025 INFO: 项目 250 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,025 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,026 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,026 INFO:   - 数量: 23.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,027 INFO:   - 单价: 5.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,027 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,028 INFO:   - 过期日期: 2025-12-10 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,033 INFO: 项目 251 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,034 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,034 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,034 INFO:   - 数量: 47.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,034 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,034 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,034 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,036 INFO: 项目 252 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,037 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,037 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,037 INFO:   - 数量: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,037 INFO:   - 单价: 20.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,038 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,038 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,040 INFO: 项目 253 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,040 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,040 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,040 INFO:   - 数量: 96.7 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,041 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,041 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,041 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,043 INFO: 项目 254 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,043 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,043 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,044 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,044 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,044 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,044 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,046 INFO: 项目 255 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,046 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,046 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,047 INFO:   - 数量: 10.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,047 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,047 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,047 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,049 INFO: 项目 256 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,049 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,050 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,050 INFO:   - 数量: 500.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,050 INFO:   - 单价: 12.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,050 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,050 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:29:30,053 INFO: 项目 257 详细信息: [in C:\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-14 20:29:30,053 INFO:   - 供应商ID: 27 [in C:\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-14 20:29:30,053 INFO:   - 存储位置ID: 15 [in C:\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-14 20:29:30,054 INFO:   - 数量: 100.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-14 20:29:30,054 INFO:   - 单价: 7.0 [in C:\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-14 20:29:30,055 INFO:   - 生产日期: 2025-06-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-14 20:29:30,057 INFO:   - 过期日期: 2025-07-13 [in C:\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-14 20:31:56,601 INFO: 成功生成二维码，数据: http://xiaoyuanst.com/food-trace/qr/B20250613ac07dd/1... [in C:\StudentsCMSSP\app\routes\stock_in.py:2987]
2025-06-14 20:35:21,203 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 20:35:53,411 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 20:35:58,119 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 20:36:00,923 INFO: 库存汇总查询条件: 状态=正常, 最小数量=0.001 [in C:\StudentsCMSSP\app\routes\inventory.py:287]
2025-06-14 20:36:20,638 INFO: 库存汇总查询条件: 状态=正常, 最小数量=0.001 [in C:\StudentsCMSSP\app\routes\inventory.py:287]
2025-06-14 20:37:35,978 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 20:37:36,017 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-14 20:39:52,439 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 20:39:52,456 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-14 20:40:06,219 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 20:40:06,296 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-14 20:45:15,676 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 20:45:15,714 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-14 20:56:36,481 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-14 20:56:36,481 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-14 20:56:36,493 INFO: 找到有发生额的科目数量: 0 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
