http://127.0.0.1:8080/financial/ledgers/general-- 删除 MenuPlan 和 MenuRecipe 表的 SQL 脚本
-- 执行前请确保已经备份相关数据

USE [StudentsCMSSP]
GO

PRINT '开始删除 MenuPlan 和 MenuRecipe 相关表和约束...'
PRINT ''

-- 1. 首先移除外键约束
PRINT '1. 移除外键约束...'

-- 移除 ConsumptionPlan 表中的 menu_plan_id 外键约束
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_consumption_plans_menu_plan_id')
BEGIN
    ALTER TABLE consumption_plans DROP CONSTRAINT FK_consumption_plans_menu_plan_id
    PRINT '✓ 已移除 consumption_plans.menu_plan_id 外键约束'
END
ELSE
    PRINT '- consumption_plans.menu_plan_id 外键约束不存在'

-- 移除 FoodSample 表中的 menu_plan_id 外键约束
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_food_samples_menu_plan_id')
BEGIN
    ALTER TABLE food_samples DROP CONSTRAINT FK_food_samples_menu_plan_id
    PRINT '✓ 已移除 food_samples.menu_plan_id 外键约束'
END
ELSE
    PRINT '- food_samples.menu_plan_id 外键约束不存在'

-- 移除 MenuRecipe 表中的外键约束
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_menu_recipes_menu_plan_id')
BEGIN
    ALTER TABLE menu_recipes DROP CONSTRAINT FK_menu_recipes_menu_plan_id
    PRINT '✓ 已移除 menu_recipes.menu_plan_id 外键约束'
END
ELSE
    PRINT '- menu_recipes.menu_plan_id 外键约束不存在'

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_menu_recipes_recipe_id')
BEGIN
    ALTER TABLE menu_recipes DROP CONSTRAINT FK_menu_recipes_recipe_id
    PRINT '✓ 已移除 menu_recipes.recipe_id 外键约束'
END
ELSE
    PRINT '- menu_recipes.recipe_id 外键约束不存在'

-- 移除 MenuPlan 表中的外键约束
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_menu_plans_area_id')
BEGIN
    ALTER TABLE menu_plans DROP CONSTRAINT FK_menu_plans_area_id
    PRINT '✓ 已移除 menu_plans.area_id 外键约束'
END
ELSE
    PRINT '- menu_plans.area_id 外键约束不存在'

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_menu_plans_created_by')
BEGIN
    ALTER TABLE menu_plans DROP CONSTRAINT FK_menu_plans_created_by
    PRINT '✓ 已移除 menu_plans.created_by 外键约束'
END
ELSE
    PRINT '- menu_plans.created_by 外键约束不存在'

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_menu_plans_approved_by')
BEGIN
    ALTER TABLE menu_plans DROP CONSTRAINT FK_menu_plans_approved_by
    PRINT '✓ 已移除 menu_plans.approved_by 外键约束'
END
ELSE
    PRINT '- menu_plans.approved_by 外键约束不存在'

PRINT ''

-- 2. 删除索引
PRINT '2. 删除相关索引...'

-- 删除 MenuPlan 相关索引
DECLARE @sql NVARCHAR(MAX)
DECLARE index_cursor CURSOR FOR
SELECT 'DROP INDEX ' + i.name + ' ON ' + t.name
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name IN ('menu_plans', 'menu_recipes')
AND i.name IS NOT NULL
AND i.is_primary_key = 0
AND i.is_unique_constraint = 0

OPEN index_cursor
FETCH NEXT FROM index_cursor INTO @sql

WHILE @@FETCH_STATUS = 0
BEGIN
    BEGIN TRY
        EXEC sp_executesql @sql
        PRINT '✓ 已删除索引: ' + @sql
    END TRY
    BEGIN CATCH
        PRINT '- 删除索引失败: ' + @sql + ' - ' + ERROR_MESSAGE()
    END CATCH
    
    FETCH NEXT FROM index_cursor INTO @sql
END

CLOSE index_cursor
DEALLOCATE index_cursor

PRINT ''

-- 3. 删除表
PRINT '3. 删除表...'

-- 删除 MenuRecipe 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
BEGIN
    DROP TABLE menu_recipes
    PRINT '✓ 已删除 menu_recipes 表'
END
ELSE
    PRINT '- menu_recipes 表不存在'

-- 删除 MenuPlan 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    DROP TABLE menu_plans
    PRINT '✓ 已删除 menu_plans 表'
END
ELSE
    PRINT '- menu_plans 表不存在'

PRINT ''

-- 4. 验证删除结果
PRINT '4. 验证删除结果...'

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
    PRINT '✓ menu_plans 表已成功删除'
ELSE
    PRINT '✗ menu_plans 表仍然存在'

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
    PRINT '✓ menu_recipes 表已成功删除'
ELSE
    PRINT '✗ menu_recipes 表仍然存在'

-- 检查 ConsumptionPlan 表中的 menu_plan_id 字段是否仍然存在
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
    PRINT '- consumption_plans.menu_plan_id 字段仍然存在（用于历史数据兼容）'
ELSE
    PRINT '- consumption_plans.menu_plan_id 字段不存在'

-- 检查 FoodSample 表中的 menu_plan_id 字段是否仍然存在
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('food_samples') AND name = 'menu_plan_id')
    PRINT '- food_samples.menu_plan_id 字段仍然存在（用于历史数据兼容）'
ELSE
    PRINT '- food_samples.menu_plan_id 字段不存在'

PRINT ''
PRINT '🎉 MenuPlan 和 MenuRecipe 表删除完成！'
PRINT ''
PRINT '📋 删除总结：'
PRINT '• 已删除 menu_plans 表'
PRINT '• 已删除 menu_recipes 表'
PRINT '• 已移除相关外键约束'
PRINT '• 已删除相关索引'
PRINT '• 保留了 consumption_plans 和 food_samples 表中的 menu_plan_id 字段用于历史数据兼容'
PRINT ''
PRINT '⚠️  注意事项：'
PRINT '• 系统现在完全依赖 WeeklyMenu + WeeklyMenuRecipe 进行菜单管理'
PRINT '• 消耗计划创建现在基于周菜单数据'
PRINT '• 食品留样现在基于日期、餐次和区域进行关联'
PRINT '• 请确保所有相关代码已经更新完成'
PRINT ''
