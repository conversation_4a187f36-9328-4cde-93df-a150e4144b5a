{% extends 'base.html' %}

{% block title %}采购订单列表 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 采购订单列表页面专用样式 - 基于系统级CSS */

/* 表格列宽优化 */
.order-number-column {
    width: 150px;
    word-wrap: break-word;
}
.datetime-column {
    width: 100px;
    font-size: 0.8rem;
}
.amount-column {
    width: 100px;
    text-align: right;
}
.status-column {
    width: 140px;
}
.action-column {
    width: 300px;
    min-width: 300px;
}

/* 日期时间显示优化 */
.datetime-display {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.datetime-display .date {
    font-weight: 500 !important;
    color: #495057;
}

.datetime-display .time {
    color: #6c757d;
    font-size: 0.75rem;
}

/* 状态列布局优化 */
.status-column-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
}

/* 操作按钮组优化 */
.table-actions {
    white-space: nowrap;
}

.table-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.2rem;
}

/* 移动端卡片优化 */
.mobile-order-card {
    border-left: 4px solid #e9ecef;
    transition: all 0.2s ease;
}

.mobile-order-card.status-pending {
    border-left-color: #ffc107;
}

.mobile-order-card.status-confirmed {
    border-left-color: #17a2b8;
}

.mobile-order-card.status-delivered {
    border-left-color: #28a745;
}

.mobile-order-card.status-cancelled {
    border-left-color: #dc3545;
}

.mobile-order-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-1px);
}

/* 筛选工具栏优化 */
.filter-toolbar {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.filter-toolbar .form-control,
.filter-toolbar .form-select {
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* 表格工具栏 */
.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.table-toolbar h6 {
    margin: 0;
    color: #495057;
}

.table-toolbar .badge {
    font-size: 0.7rem;
}

/* 空状态样式 */
.table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.table-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-empty h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.table-empty p {
    margin: 0;
    font-size: 0.875rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .action-column {
        min-width: 280px;
    }

    .table-actions .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin: 0 0.1rem;
    }

    .filter-toolbar {
        padding: 0.75rem;
    }

    .table-toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2>采购订单管理</h2>
        </div>
        <div class="col-12 mt-2">
            <!-- 移动端按钮 -->
            <div class="action-buttons d-md-none">
                <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#createFromMenuModal">
                    <i class="fas fa-plus"></i> 从周菜单创建
                </button>
                <a href="{{ url_for('purchase_order.create_form') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-edit"></i> 手动创建
                </a>
            </div>
            <!-- 桌面端按钮 -->
            <div class="d-none d-md-block text-right">
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createFromMenuModal">
                        <i class="fas fa-plus"></i> 从周菜单创建
                    </button>
                    <a href="{{ url_for('purchase_order.create_form') }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> 手动创建
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row">
                <div class="col-12 col-md-3 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend d-none d-md-flex">
                            <span class="input-group-text">订单号</span>
                        </div>
                        <input type="text" class="form-control" name="order_number" placeholder="输入订单号搜索">
                    </div>
                </div>
                <div class="col-12 col-md-2 mb-2">
                    <select class="form-control" name="status">
                        <option value="">所有状态</option>
                        <option value="待确认">待确认</option>
                        <option value="已确认">已确认</option>
                        <option value="已送达">已送达</option>
                        <option value="已取消">已取消</option>
                    </select>
                </div>
                <div class="col-12 col-md-3 mb-2">
                    <div class="row">
                        <div class="col-6">
                            <input type="date" class="form-control" name="start_date" placeholder="开始日期">
                        </div>
                        <div class="col-6">
                            <input type="date" class="form-control" name="end_date" placeholder="结束日期">
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-2 mb-2">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i> <span class="d-none d-md-inline">搜索</span>
                    </button>
                </div>
                <div class="col-6 col-md-2 mb-2">
                    <button type="reset" class="btn btn-secondary btn-block">
                        <i class="fas fa-redo"></i> <span class="d-none d-md-inline">重置</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="card">
        <div class="card-body">
            <!-- 表格工具栏 -->
            <div class="table-toolbar">
                <div class="toolbar-left">
                    <h6 class="mb-0">
                        <i class="fas fa-list"></i> 采购订单列表
                        <span class="badge badge-primary ml-2">{{ orders.items|length }} 条记录</span>
                    </h6>
                </div>
                <div class="toolbar-right">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        表格已优化显示，支持紧凑排版
                    </small>
                </div>
            </div>

            <!-- 桌面端表格视图 -->
            <div class="table-responsive d-none d-lg-block">
                <table class="table table-hover table-compact">
                    <thead>
                        <tr>
                            <th class="order-number-column">订单号</th>
                            <th class="datetime-column">创建时间</th>
                            <th class="datetime-column">送货日期</th>
                            <th class="amount-column">总金额</th>
                            <th class="status-column">状态</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr class="{% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}table-light{% elif orders_status_info[order.id]['consumption_status'] == '部分消耗' %}table-warning{% endif %}">
                            <td class="order-number-column">
                                <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="text-primary font-weight-medium">
                                    {{ order.order_number }}
                                </a>
                            </td>
                            <td class="datetime-column">
                                <div class="datetime-display">
                                    <span class="date">{{ order.order_date|format_datetime('%m-%d') if order.order_date else '-' }}</span>
                                    <span class="time">{{ order.order_date|format_datetime('%H:%M') if order.order_date else '' }}</span>
                                </div>
                            </td>
                            <td class="datetime-column">{{ order.delivery_date|format_datetime('%m-%d') if order.delivery_date else '-' }}</td>
                            <td class="amount-column">
                                <span class="amount-display">¥{{ "%.2f"|format(order.total_amount) }}</span>
                            </td>
                            <td class="status-column">
                                <div class="status-column-content">
                                    <!-- 订单状态 -->
                                    {% if order.status == '待确认' %}
                                    <span class="purchase-order-status status-pending">待确认</span>
                                    {% elif order.status == '已确认' %}
                                    <span class="purchase-order-status status-confirmed">已确认</span>
                                    {% elif order.status == '准备入库' %}
                                    <span class="purchase-order-status status-delivered">准备入库</span>
                                    {% elif order.status == '已取消' %}
                                    <span class="purchase-order-status status-cancelled">已取消</span>
                                    {% else %}
                                    <span class="purchase-order-status">{{ order.get_status_display() }}</span>
                                    {% endif %}

                                    <!-- 入库状态 -->
                                    {% if orders_status_info[order.id]['has_stock_in'] %}
                                        {% set stock_in_status = orders_status_info[order.id]['stock_in_status'] %}
                                        {% if stock_in_status in ['已入库', '已审核'] %}
                                            <span class="badge badge-success">入库已完成</span>
                                        {% elif stock_in_status == '待审核' %}
                                            <span class="badge badge-warning">{{ stock_in_status }}</span>
                                        {% elif stock_in_status == '已取消' %}
                                            <span class="badge badge-danger">{{ stock_in_status }}</span>
                                        {% else %}
                                            <span class="badge badge-info">{{ stock_in_status }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge badge-secondary">未入库</span>
                                    {% endif %}

                                    <!-- 消耗状态 -->
                                    {% if orders_status_info[order.id]['consumption_status'] != '未消耗' %}
                                        <span class="badge {% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}badge-success{% else %}badge-warning{% endif %}">
                                            {{ orders_status_info[order.id]['consumption_status'] }}
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="action-column">
                                <div class="purchase-order-actions">
                                    <!-- 1. 查看按钮 - 始终显示 -->
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-outline-primary btn-sm" title="查看详情">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>

                                    <!-- 2. 根据订单状态和入库状态显示相应的操作按钮 -->
                                    {% set stock_in_status = orders_status_info[order.id]['stock_in_status'] %}
                                    {% set has_stock_in = orders_status_info[order.id]['has_stock_in'] %}

                                    <!-- 检查是否已入库完成 - 这是最高优先级状态 -->
                                    {% if has_stock_in and stock_in_status in ['已入库', '已审核'] %}
                                        <!-- 入库已完成：不显示任何操作按钮，只在后面显示"入库已完成"状态 -->
                                    {% elif order.status == '待确认' %}
                                        <!-- 待确认状态：可以确认或取消 -->
                                        <button type="button" class="btn btn-outline-success btn-sm confirm-btn" data-id="{{ order.id }}" title="确认订单">
                                            <i class="fas fa-check"></i> 确认
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-id="{{ order.id }}" title="取消订单">
                                            <i class="fas fa-times"></i> 取消
                                        </button>
                                    {% elif order.status == '已确认' %}
                                        <!-- 已确认状态：可以标记送达 -->
                                        <button type="button" class="btn btn-outline-info btn-sm deliver-btn" data-id="{{ order.id }}" title="标记送达">
                                            <i class="fas fa-truck"></i> 送达
                                        </button>
                                        {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-id="{{ order.id }}" title="取消订单">
                                            <i class="fas fa-times"></i> 取消
                                        </button>
                                        {% endif %}
                                    {% elif order.status == '准备入库' %}
                                        <!-- 准备入库状态：可以创建入库单或查看入库单 -->
                                        {% if not has_stock_in %}
                                            <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-outline-success btn-sm" title="创建入库单">
                                                <i class="fas fa-dolly"></i> 入库
                                            </a>
                                        {% else %}
                                            <a href="{{ url_for('stock_in.view_details', id=orders_status_info[order.id]['stock_in_id']) }}" class="btn btn-outline-info btn-sm" title="查看入库单">
                                                <i class="fas fa-clipboard-list"></i> 入库单
                                            </a>
                                        {% endif %}
                                        {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-id="{{ order.id }}" title="取消订单">
                                            <i class="fas fa-times"></i> 取消
                                        </button>
                                        {% endif %}
                                    {% elif order.status == '已取消' %}
                                        <!-- 已取消状态：只能查看和删除 -->
                                        {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                        <button type="button" class="btn btn-outline-danger btn-sm delete-btn" data-id="{{ order.id }}" title="删除订单">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                        {% endif %}
                                    {% endif %}

                                    <!-- 3. 入库完成后的状态显示 -->
                                    {% if orders_status_info[order.id]['has_stock_in'] and orders_status_info[order.id]['stock_in_status'] in ['已入库', '已审核'] %}
                                        <span class="btn btn-outline-success btn-sm disabled" title="入库已完成">
                                            <i class="fas fa-check-circle"></i> 入库已完成
                                        </span>
                                    {% endif %}

                                    <!-- 4. 打印按钮 - 除了待确认状态外都可以打印 -->
                                    {% if order.status != '待确认' %}
                                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn btn-outline-secondary btn-sm" title="打印订单" target="_blank">
                                        <i class="fas fa-print"></i> 打印
                                    </a>
                                    {% endif %}

                                    <!-- 5. 删除按钮 - 只有特定状态且未入库的订单可以删除 -->
                                    {% if (order.status == '待确认' or (order.status == '已取消' and not has_stock_in)) and (current_user.is_admin() or current_user.has_role('学校管理员')) %}
                                    <button type="button" class="btn btn-outline-danger btn-sm delete-btn" data-id="{{ order.id }}" title="删除订单">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="table-empty">
                                <i class="fas fa-inbox"></i>
                                <h5>暂无采购订单</h5>
                                <p>您可以创建新的采购订单或调整筛选条件</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 移动端卡片视图 -->
            <div class="d-lg-none">
                {% for order in orders.items %}
                <div class="card mb-3 mobile-order-card {% if order.status == '待确认' %}status-pending{% elif order.status == '已确认' %}status-confirmed{% elif order.status == '准备入库' %}status-delivered{% elif order.status == '已取消' %}status-cancelled{% endif %}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <h6 class="card-title mb-2">
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="text-primary">
                                        {{ order.order_number }}
                                    </a>
                                </h6>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">创建时间</small>
                                <div class="small">{{ order.order_date|format_datetime('%m-%d %H:%M') if order.order_date else '-' }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">送货日期</small>
                                <div class="small">{{ order.delivery_date|format_datetime('%m-%d') if order.delivery_date else '-' }}</div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">总金额</small>
                                <div class="purchase-order-amount">¥{{ "%.2f"|format(order.total_amount) }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">状态</small>
                                <div>
                                    {% if order.status == '待确认' %}
                                    <span class="purchase-order-status status-pending">待确认</span>
                                    {% elif order.status == '已确认' %}
                                    <span class="purchase-order-status status-confirmed">已确认</span>
                                    {% elif order.status == '准备入库' %}
                                    <span class="purchase-order-status status-delivered">准备入库</span>
                                    {% elif order.status == '已取消' %}
                                    <span class="purchase-order-status status-cancelled">已取消</span>
                                    {% else %}
                                    <span class="purchase-order-status">{{ order.get_status_display() }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">入库状态</small>
                                <div>
                                    {% if orders_status_info[order.id]['has_stock_in'] %}
                                        {% set stock_in_status = orders_status_info[order.id]['stock_in_status'] %}
                                        {% if stock_in_status in ['已入库', '已审核'] %}
                                            <span class="badge badge-success">入库已完成</span>
                                        {% elif stock_in_status == '待审核' %}
                                            <span class="badge badge-warning">{{ stock_in_status }}</span>
                                        {% elif stock_in_status == '已取消' %}
                                            <span class="badge badge-danger">{{ stock_in_status }}</span>
                                        {% else %}
                                            <span class="badge badge-info">{{ stock_in_status }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge badge-secondary">未入库</span>
                                    {% endif %}

                                    {% if orders_status_info[order.id]['consumption_status'] != '未消耗' %}
                                    <span class="badge {% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}badge-success{% else %}badge-warning{% endif %} ml-1">
                                        {{ orders_status_info[order.id]['consumption_status'] }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="btn-group btn-group-sm w-100" role="group">
                                    <!-- 查看按钮 -->
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    <!-- 根据状态显示操作按钮 -->
                                    {% set stock_in_status = orders_status_info[order.id]['stock_in_status'] %}
                                    {% set has_stock_in = orders_status_info[order.id]['has_stock_in'] %}

                                    <!-- 检查是否已入库完成 - 这是最高优先级状态 -->
                                    {% if has_stock_in and stock_in_status in ['已入库', '已审核'] %}
                                        <!-- 入库已完成：不显示操作按钮，只显示状态 -->
                                    {% elif order.status == '待确认' %}
                                        <button type="button" class="btn btn-outline-success confirm-btn" data-id="{{ order.id }}">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger cancel-btn" data-id="{{ order.id }}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% elif order.status == '已确认' %}
                                        <button type="button" class="btn btn-outline-info deliver-btn" data-id="{{ order.id }}">
                                            <i class="fas fa-truck"></i>
                                        </button>
                                    {% elif order.status == '准备入库' %}
                                        {% if not has_stock_in %}
                                            <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-outline-success">
                                                <i class="fas fa-dolly"></i>
                                            </a>
                                        {% else %}
                                            <a href="{{ url_for('stock_in.view_details', id=orders_status_info[order.id]['stock_in_id']) }}" class="btn btn-outline-info">
                                                <i class="fas fa-clipboard-list"></i>
                                            </a>
                                        {% endif %}
                                    {% endif %}

                                    <!-- 打印按钮 -->
                                    {% if order.status != '待确认' %}
                                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn btn-outline-secondary" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5>暂无采购订单</h5>
                    <p class="text-muted">您可以创建新的采购订单或调整筛选条件</p>
                </div>
                {% endfor %}
            </div>

            <!-- 分页导航 -->
            {% if orders.pages > 1 %}
            <nav aria-label="采购订单分页">
                <ul class="pagination justify-content-center">
                    <!-- 上一页 -->
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.prev_num, **request.args) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </span>
                    </li>
                    {% endif %}

                    <!-- 页码 -->
                    {% for page_num in orders.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('purchase_order.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    <!-- 下一页 -->
                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.next_num, **request.args) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                    {% endif %}
                </ul>

                <!-- 分页信息 -->
                <div class="text-center mt-2">
                    <small class="text-muted">
                        显示第 {{ (orders.page - 1) * orders.per_page + 1 }} - {{ orders.page * orders.per_page if orders.page * orders.per_page <= orders.total else orders.total }} 条，
                        共 {{ orders.total }} 条记录，第 {{ orders.page }} / {{ orders.pages }} 页
                    </small>
                </div>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 从周菜单创建模态框 -->
<div class="modal fade" id="createFromMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择采购区域</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createFromMenuForm">
                    <div class="form-group">
                        <label for="areaSelect">请选择区域：</label>
                        <select class="form-control" id="areaSelect" required>
                            <option value="">请选择...</option>
                            {% for area in areas %}
                            <option value="{{ area.id }}">{{ area.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAreaBtn">
                    <i class="fas fa-arrow-right"></i> 下一步
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 注意：只有未入库的订单才能删除。已入库或已创建入库单的订单无法删除。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    let currentOrderId = null;

    // 用户权限检查
    window.userIsAdmin = {{ 'true' if current_user.is_admin() or current_user.has_role('学校管理员') else 'false' }};

    // 初始化日期范围
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    $('input[name="start_date"]').val(thirtyDaysAgo.toISOString().split('T')[0]);
    $('input[name="end_date"]').val(today.toISOString().split('T')[0]);

    // 确认订单
    $('.confirm-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/confirm`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单确认成功');
                    // 更新页面状态而不是重新加载
                    updateOrderStatus(currentOrderId, '已确认', 'status-info');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '已确认');
                } else {
                    showErrorMessage(response.message || '确认订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '确认订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
                $('#confirmModal').modal('hide');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        if (!currentOrderId) return;

        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            showErrorMessage('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/cancel`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单取消成功');
                    // 更新页面状态
                    updateOrderStatus(currentOrderId, '已取消', 'status-danger');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '已取消');
                    // 清空取消原因
                    $('#cancelReason').val('');
                } else {
                    showErrorMessage(response.message || '取消订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '取消订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
                $('#cancelModal').modal('hide');
            }
        });
    });

    // 标记送达
    $('.deliver-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        if (!currentOrderId) return;

        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/deliver`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单已标记为准备入库');
                    // 更新页面状态
                    updateOrderStatus(currentOrderId, '准备入库', 'status-success');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '准备入库');
                    // 清空备注
                    $('#deliveryNotes').val('');
                } else {
                    showErrorMessage(response.message || '标记送达失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '标记送达失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
                $('#deliverModal').modal('hide');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/delete`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单删除成功');
                    // 移除表格行
                    removeOrderRow(currentOrderId);
                } else {
                    showErrorMessage(response.message || '删除订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '删除订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });

    // 筛选表单提交
    $('#filterForm').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const params = new URLSearchParams();

        for (const [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        window.location.search = params.toString();
    });

    // 重置筛选
    $('#filterForm button[type="reset"]').click(function() {
        window.location.href = window.location.pathname;
    });

    // 确认区域选择
    $('#confirmAreaBtn').click(function() {
        const areaId = $('#areaSelect').val();
        if (!areaId) {
            alert('请选择区域');
            return;
        }
        window.location.href = '{{ url_for("purchase_order.create_from_menu") }}?area_id=' + areaId;
    });

    // 状态同步检查功能
    function checkOrderStatusUpdates() {
        // 收集当前页面所有订单ID和状态
        const orderIds = [];
        const currentStatuses = {};

        $('tbody tr').each(function() {
            const row = $(this);
            const orderBtn = row.find('.confirm-btn, .cancel-btn, .deliver-btn').first();
            const orderId = orderBtn.data('id');

            if (orderId) {
                orderIds.push(orderId);
                // 获取当前显示的状态
                const statusText = row.find('.status-badge').text().trim();
                currentStatuses[orderId] = statusText;
            }
        });

        if (orderIds.length === 0) return;

        $.ajax({
            url: '{{ url_for("purchase_order.check_updates") }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                order_ids: orderIds,
                current_statuses: currentStatuses
            }),
            success: function(response) {
                if (response.success && response.updates) {
                    response.updates.forEach(function(update) {
                        if (update.status_changed) {
                            // 状态发生变化，更新页面显示
                            const statusClass = getStatusClass(update.new_status);
                            updateOrderStatus(update.order_id, update.new_status, statusClass);
                            updateOrderActions(update.order_id, update.new_status);

                            // 显示状态变更通知
                            showInfoMessage(`订单 ${update.order_number} 状态已更新为：${update.new_status}`);
                        }
                    });
                }
            },
            error: function() {
                // 静默处理错误，避免影响用户体验
                console.log('状态同步检查失败');
            }
        });
    }

    // 获取状态对应的CSS类
    function getStatusClass(status) {
        switch(status) {
            case '待确认': return 'status-warning';
            case '已确认': return 'status-info';
            case '准备入库': return 'status-success';
            case '已取消': return 'status-danger';
            default: return 'status-secondary';
        }
    }

    // 显示信息消息
    function showInfoMessage(message) {
        const alertHtml = `
            <div class="alert alert-info alert-dismissible fade show alert-message" role="alert">
                <i class="fas fa-info-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);

        setTimeout(function() {
            $('.alert-message').fadeOut();
        }, 4000);
    }

    // 每30秒检查一次状态更新
    setInterval(checkOrderStatusUpdates, 30000);

    // 页面获得焦点时也检查一次（用户切换回页面时）
    $(window).focus(function() {
        checkOrderStatusUpdates();
    });

    // 辅助函数：显示成功消息
    function showSuccessMessage(message) {
        // 移除现有的消息
        $('.alert-message').remove();

        // 创建成功消息
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show alert-message" role="alert">
                <i class="fas fa-check-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // 插入到页面顶部
        $('.container-fluid').prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert-message').fadeOut();
        }, 3000);
    }

    // 辅助函数：显示错误消息
    function showErrorMessage(message) {
        // 移除现有的消息
        $('.alert-message').remove();

        // 创建错误消息
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show alert-message" role="alert">
                <i class="fas fa-exclamation-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // 插入到页面顶部
        $('.container-fluid').prepend(alertHtml);

        // 5秒后自动消失
        setTimeout(function() {
            $('.alert-message').fadeOut();
        }, 5000);
    }

    // 辅助函数：更新订单状态
    function updateOrderStatus(orderId, newStatus, statusClass) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        const statusCell = row.find('td:nth-child(6)'); // 状态列

        // 移除旧的状态类
        const statusBadge = statusCell.find('.status-badge');
        statusBadge.removeClass('status-warning status-info status-success status-danger');

        // 添加新的状态类和文本
        statusBadge.addClass(statusClass).text(newStatus);

        // 添加动画效果
        statusCell.addClass('table-success');
        setTimeout(function() {
            statusCell.removeClass('table-success');
        }, 2000);
    }

    // 辅助函数：更新操作按钮
    function updateOrderActions(orderId, newStatus) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        const actionCell = row.find('.purchase-order-actions');

        // 根据新状态更新按钮显示
        const confirmBtn = actionCell.find('.confirm-btn');
        const cancelBtn = actionCell.find('.cancel-btn');
        const deliverBtn = actionCell.find('.deliver-btn');
        const deleteBtn = actionCell.find('.delete-btn');
        const stockInBtn = actionCell.find('a[href*="stock_in_wizard"]');
        const printBtn = actionCell.find('a[href*="print_order"]');

        // 隐藏所有状态相关按钮
        confirmBtn.hide();
        cancelBtn.hide();
        deliverBtn.hide();
        deleteBtn.hide();
        stockInBtn.hide();

        // 根据新状态显示相应按钮
        switch(newStatus) {
            case '待确认':
                confirmBtn.show();
                cancelBtn.show();
                printBtn.hide(); // 待确认状态不显示打印按钮
                break;
            case '已确认':
                deliverBtn.show();
                printBtn.show();
                break;
            case '准备入库':
                stockInBtn.show();
                printBtn.show();
                break;
            case '入库已完成':
                // 入库已完成是最终状态，只显示打印按钮
                printBtn.show();
                break;
            case '已取消':
                printBtn.show();
                // 管理员可以删除已取消的订单
                if (window.userIsAdmin) {
                    deleteBtn.show();
                }
                break;
            default:
                printBtn.show();
                break;
        }

        // 移动端按钮组也需要更新
        const mobileCard = $(`.mobile-order-card:has(button[data-id="${orderId}"])`);
        if (mobileCard.length > 0) {
            const mobileBtnGroup = mobileCard.find('.btn-group');
            const mobileConfirmBtn = mobileBtnGroup.find('.confirm-btn');
            const mobileCancelBtn = mobileBtnGroup.find('.cancel-btn');
            const mobileDeliverBtn = mobileBtnGroup.find('.deliver-btn');
            const mobileStockInBtn = mobileBtnGroup.find('a[href*="stock_in_wizard"]');
            const mobilePrintBtn = mobileBtnGroup.find('a[href*="print_order"]');

            // 隐藏移动端按钮
            mobileConfirmBtn.hide();
            mobileCancelBtn.hide();
            mobileDeliverBtn.hide();
            mobileStockInBtn.hide();

            // 根据状态显示移动端按钮
            switch(newStatus) {
                case '待确认':
                    mobileConfirmBtn.show();
                    mobileCancelBtn.show();
                    mobilePrintBtn.hide();
                    break;
                case '已确认':
                    mobileDeliverBtn.show();
                    mobilePrintBtn.show();
                    break;
                case '准备入库':
                    mobileStockInBtn.show();
                    mobilePrintBtn.show();
                    break;
                case '入库已完成':
                    // 入库已完成是最终状态，只显示打印按钮
                    mobilePrintBtn.show();
                    break;
                default:
                    mobilePrintBtn.show();
                    break;
            }
        }
    }

    // 辅助函数：移除订单行
    function removeOrderRow(orderId) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        row.fadeOut(500, function() {
            $(this).remove();

            // 检查是否还有订单，如果没有则显示空状态
            const remainingRows = $('tbody tr').length;
            if (remainingRows === 0) {
                const emptyRowHtml = `
                    <tr>
                        <td colspan="7" class="table-empty">
                            <i class="fas fa-inbox"></i>
                            <h5>暂无采购订单</h5>
                            <p>您可以创建新的采购订单或调整筛选条件</p>
                        </td>
                    </tr>
                `;
                $('tbody').html(emptyRowHtml);
            }
        });
    }

    // 页面加载完成后重新绑定事件（用于动态添加的元素）
    function rebindEvents() {
        // 重新绑定确认按钮
        $('.confirm-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#confirmModal').modal('show');
        });

        // 重新绑定取消按钮
        $('.cancel-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#cancelModal').modal('show');
        });

        // 重新绑定送达按钮
        $('.deliver-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#deliverModal').modal('show');
        });

        // 重新绑定删除按钮
        $('.delete-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#deleteModal').modal('show');
        });
    }

    // 自动刷新功能（可选）
    let autoRefreshInterval;

    function startAutoRefresh() {
        // 每30秒检查一次状态更新
        autoRefreshInterval = setInterval(function() {
            // 只在没有模态框打开时才刷新
            if (!$('.modal').hasClass('show')) {
                checkOrderUpdates();
            }
        }, 30000);
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    function checkOrderUpdates() {
        // 获取当前页面的所有订单ID
        const orderIds = [];
        $('.confirm-btn, .cancel-btn, .deliver-btn').each(function() {
            const orderId = $(this).data('id');
            if (orderId && orderIds.indexOf(orderId) === -1) {
                orderIds.push(orderId);
            }
        });

        if (orderIds.length === 0) return;

        // 发送请求检查状态更新
        $.ajax({
            url: '/purchase-order/check-updates',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ order_ids: orderIds }),
            success: function(response) {
                if (response.success && response.updates) {
                    // 处理状态更新
                    response.updates.forEach(function(update) {
                        if (update.status_changed) {
                            updateOrderStatus(update.order_id, update.new_status, getStatusClass(update.new_status));
                            updateOrderActions(update.order_id, update.new_status);

                            // 显示更新通知
                            showSuccessMessage(`订单 ${update.order_number} 状态已更新为：${update.new_status}`);
                        }
                    });
                }
            },
            error: function() {
                // 静默处理错误，不影响用户体验
                console.log('检查订单更新失败');
            }
        });
    }

    function getStatusClass(status) {
        const statusClassMap = {
            '待确认': 'status-warning',
            '已确认': 'status-info',
            '准备入库': 'status-success',
            '已送达': 'status-success',
            '入库已完成': 'status-success',
            '已取消': 'status-danger'
        };
        return statusClassMap[status] || 'status-warning';
    }

    // 页面可见性API - 当页面不可见时停止自动刷新
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // 启动自动刷新（可选功能，用户可以通过设置开启/关闭）
    // startAutoRefresh();
});
</script>
{% endblock %}
