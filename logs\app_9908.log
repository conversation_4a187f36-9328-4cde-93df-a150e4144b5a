2025-06-14 12:28:18,848 INFO: 应用启动 - PID: 9908 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-14 12:28:26,989 ERROR: 获取可用食材失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]选择列表中的列 'ingredients.name' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 (8120) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT ingredients.id AS ingredients_id, ingredients.name AS ingredients_name, ingredients.category AS ingredients_category, ingredients.category_id AS ingredients_category_id, ingredients.area_id AS ingredients_area_id, ingredients.unit AS ingredients_unit, ingredients.standard_unit AS ingredients_standard_unit, ingredients.base_image AS ingredients_base_image, ingredients.storage_temp AS ingredients_storage_temp, ingredients.storage_condition AS ingredients_storage_condition, ingredients.shelf_life AS ingredients_shelf_life, ingredients.specification AS ingredients_specification, ingredients.nutrition_info AS ingredients_nutrition_info, ingredients.is_condiment AS ingredients_is_condiment, ingredients.is_global AS ingredients_is_global, ingredients.status AS ingredients_status, ingredients.created_at AS ingredients_created_at, ingredients.updated_at AS ingredients_updated_at, count(supplier_products.id) AS supplier_count 
FROM ingredients JOIN supplier_products ON supplier_products.ingredient_id = ingredients.id AND supplier_products.is_available = ? AND supplier_products.shelf_status = ? JOIN suppliers ON suppliers.id = supplier_products.supplier_id AND suppliers.status = ? JOIN supplier_school_relations ON supplier_school_relations.supplier_id = suppliers.id AND supplier_school_relations.area_id IN (?) AND supplier_school_relations.status = ? 
WHERE ingredients.status = ? GROUP BY ingredients.id ORDER BY ingredients.name
 OFFSET ? ROWS
 FETCH FIRST ? ROWS ONLY]
[parameters: (1, 1, 1, 44, 1, 1, 0, 20)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\StudentsCMSSP\app\routes\purchase_order.py:2018]
2025-06-14 18:45:25,695 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 18:45:25,967 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-14 18:48:34,367 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 18:48:59,770 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 19:10:47,562 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-14 19:10:48,411 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
