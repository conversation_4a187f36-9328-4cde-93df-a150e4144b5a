2025-06-11 10:51:41,287 INFO: 应用启动 - PID: 10028 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 10:51:44,919 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 10:51:44,935 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 10:51:50,324 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 10:51:50,324 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 10:51:50,325 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
2025-06-11 10:51:52,004 INFO: 查询到 10 个待生成应付账款的入库单 [in D:\StudentsCMSSP\app\routes\financial\payables.py:412]
2025-06-11 10:51:52,005 INFO: 入库单: RK20250603125249, 状态: 已入库, 应付账款ID: None, 总金额: 68000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,005 INFO: 入库单: RK20250602204426, 状态: 已入库, 应付账款ID: None, 总金额: 396046.90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,005 INFO: 入库单: RK20250601021022, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250601021012, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250529123839, 状态: 已入库, 应付账款ID: None, 总金额: 409402.28 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250528223445, 状态: 已入库, 应付账款ID: None, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250528133013, 状态: 已入库, 应付账款ID: None, 总金额: 75000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250527155522, 状态: 已入库, 应付账款ID: None, 总金额: 30000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,006 INFO: 入库单: RK20250526165105, 状态: 已入库, 应付账款ID: None, 总金额: 25000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,007 INFO: 入库单: RK20250525203133, 状态: 已入库, 应付账款ID: None, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:414]
2025-06-11 10:51:52,011 INFO: 该区域总入库单数量: 16 [in D:\StudentsCMSSP\app\routes\financial\payables.py:418]
2025-06-11 10:51:52,018 INFO: 该区域已入库的入库单数量: 10 [in D:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-11 10:51:55,267 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:121]
2025-06-11 10:51:55,267 INFO: 请求数据: {'stock_in_id': 93} [in D:\StudentsCMSSP\app\routes\financial\payables.py:125]
2025-06-11 10:51:55,267 INFO: 入库单ID: 93 [in D:\StudentsCMSSP\app\routes\financial\payables.py:128]
2025-06-11 10:51:55,268 INFO: 查询入库单: ID=93, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:135]
2025-06-11 10:51:55,271 INFO: 找到入库单: RK20250603125249, 状态: 已入库, 总金额: 68000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:145]
2025-06-11 10:51:55,271 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:148]
2025-06-11 10:51:55,275 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:155]
2025-06-11 10:51:55,276 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:161]
2025-06-11 10:51:55,276 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:166]
2025-06-11 10:51:55,280 INFO: 生成首个应付账款编号: AP20250611001 [in D:\StudentsCMSSP\app\routes\financial\payables.py:179]
