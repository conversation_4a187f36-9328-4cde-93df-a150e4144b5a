2025-06-11 15:47:42,842 INFO: 应用启动 - PID: 17196 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 18:04:23,656 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-11 18:04:23,656 INFO: 请求数据: {'stock_in_id': 87} [in D:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-11 18:04:23,656 INFO: 入库单ID: 87 [in D:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-11 18:04:23,656 INFO: 查询入库单: ID=87, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-11 18:04:23,661 INFO: 找到入库单: RK20250528223445, 状态: 已入库, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-11 18:04:23,662 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-11 18:04:23,667 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-11 18:04:23,668 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-11 18:04:23,668 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-11 18:04:23,673 INFO: 基于最后编号 AP20250611002 生成新编号: AP20250611003 [in D:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-11 18:04:23,681 INFO: 开始查询会计科目... [in D:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-11 18:04:23,682 INFO: 查询原材料科目 (1201)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-11 18:04:23,688 INFO: 找到原材料科目: 原材料 (ID: 206) [in D:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-11 18:04:23,688 INFO: 查询应付账款科目 (2001)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-11 18:04:23,689 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in D:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-11 18:04:23,689 INFO: 准备凭证明细SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-11 18:04:23,689 INFO: 准备更新入库单SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-11 18:04:23,689 INFO: 开始执行数据库事务... [in D:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-11 18:04:23,690 INFO: 执行应付账款SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-11 18:04:23,692 INFO: 应付账款创建成功，ID: 4 [in D:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-11 18:04:23,692 INFO: 执行财务凭证SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-11 18:04:23,698 INFO: 财务凭证创建成功，ID: 30 [in D:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-11 18:04:23,698 INFO: 创建凭证明细... [in D:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-11 18:04:23,709 INFO: 借方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-11 18:04:23,711 INFO: 贷方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-11 18:04:23,711 INFO: 更新入库单关联信息... [in D:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-11 18:04:23,715 INFO: 入库单更新成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-11 18:04:23,716 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-11 18:04:23,717 INFO: === 应付账款生成成功 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-11 18:04:23,717 INFO: 应付账款ID: 4 [in D:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-11 18:04:23,717 INFO: 财务凭证ID: 30 [in D:\StudentsCMSSP\app\routes\financial\payables.py:514]
2025-06-11 18:04:28,634 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-11 18:04:28,634 INFO: 请求数据: {'stock_in_id': 90} [in D:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-11 18:04:28,635 INFO: 入库单ID: 90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-11 18:04:28,635 INFO: 查询入库单: ID=90, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-11 18:04:28,636 INFO: 找到入库单: RK20250601021022, 状态: 已入库, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-11 18:04:28,636 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-11 18:04:28,637 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-11 18:04:28,637 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-11 18:04:28,638 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-11 18:04:28,639 INFO: 基于最后编号 AP20250611003 生成新编号: AP20250611004 [in D:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-11 18:04:28,640 INFO: 开始查询会计科目... [in D:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-11 18:04:28,640 INFO: 查询原材料科目 (1201)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-11 18:04:28,641 INFO: 找到原材料科目: 原材料 (ID: 206) [in D:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-11 18:04:28,641 INFO: 查询应付账款科目 (2001)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-11 18:04:28,643 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in D:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-11 18:04:28,643 INFO: 准备凭证明细SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-11 18:04:28,644 INFO: 准备更新入库单SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-11 18:04:28,644 INFO: 开始执行数据库事务... [in D:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-11 18:04:28,644 INFO: 执行应付账款SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-11 18:04:28,648 INFO: 应付账款创建成功，ID: 5 [in D:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-11 18:04:28,649 INFO: 执行财务凭证SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-11 18:04:28,652 INFO: 财务凭证创建成功，ID: 31 [in D:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-11 18:04:28,652 INFO: 创建凭证明细... [in D:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-11 18:04:28,653 INFO: 借方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-11 18:04:28,654 INFO: 贷方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-11 18:04:28,655 INFO: 更新入库单关联信息... [in D:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-11 18:04:28,655 INFO: 入库单更新成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-11 18:04:28,657 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-11 18:04:28,657 INFO: === 应付账款生成成功 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-11 18:04:28,657 INFO: 应付账款ID: 5 [in D:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-11 18:04:28,657 INFO: 财务凭证ID: 31 [in D:\StudentsCMSSP\app\routes\financial\payables.py:514]
2025-06-11 18:04:32,775 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-11 18:04:32,775 INFO: 请求数据: {'stock_in_id': 77} [in D:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-11 18:04:32,775 INFO: 入库单ID: 77 [in D:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-11 18:04:32,776 INFO: 查询入库单: ID=77, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-11 18:04:32,777 INFO: 找到入库单: RK20250525203133, 状态: 已入库, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-11 18:04:32,778 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-11 18:04:32,779 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-11 18:04:32,779 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-11 18:04:32,779 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-11 18:04:32,781 INFO: 基于最后编号 AP20250611004 生成新编号: AP20250611005 [in D:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-11 18:04:32,783 INFO: 开始查询会计科目... [in D:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-11 18:04:32,783 INFO: 查询原材料科目 (1201)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-11 18:04:32,784 INFO: 找到原材料科目: 原材料 (ID: 206) [in D:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-11 18:04:32,784 INFO: 查询应付账款科目 (2001)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-11 18:04:32,785 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in D:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-11 18:04:32,786 INFO: 准备凭证明细SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-11 18:04:32,786 INFO: 准备更新入库单SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-11 18:04:32,786 INFO: 开始执行数据库事务... [in D:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-11 18:04:32,786 INFO: 执行应付账款SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-11 18:04:32,787 INFO: 应付账款创建成功，ID: 6 [in D:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-11 18:04:32,787 INFO: 执行财务凭证SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-11 18:04:32,788 INFO: 财务凭证创建成功，ID: 32 [in D:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-11 18:04:32,788 INFO: 创建凭证明细... [in D:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-11 18:04:32,788 INFO: 借方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-11 18:04:32,789 INFO: 贷方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-11 18:04:32,789 INFO: 更新入库单关联信息... [in D:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-11 18:04:32,790 INFO: 入库单更新成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-11 18:04:32,790 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-11 18:04:32,790 INFO: === 应付账款生成成功 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-11 18:04:32,790 INFO: 应付账款ID: 6 [in D:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-11 18:04:32,791 INFO: 财务凭证ID: 32 [in D:\StudentsCMSSP\app\routes\financial\payables.py:514]
