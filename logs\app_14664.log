2025-06-11 14:56:42,379 INFO: 应用启动 - PID: 14664 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 14:57:15,211 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:405]
2025-06-11 14:58:19,929 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            WITH subject_summary AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level,
                    -- 期初余额
                    ISNULL(opening.opening_balance, 0) as opening_balance,
                    -- 本期借方发生额
                    ISNULL(period.period_debit, 0) as period_debit,
                    -- 本期贷方发生额
                    ISNULL(period.period_credit, 0) as period_credit
                FROM accounting_subjects s
                LEFT JOIN (
                    SELECT vd.subject_id,
                           SUM(vd.debit_amount - vd.credit_amount) as opening_balance
                    FROM voucher_details vd
                    INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                    WHERE fv.area_id = ?
                    AND fv.voucher_date < ?
                    AND fv.status IN ('已审核', '已记账')
                    GROUP BY vd.subject_id
                ) opening ON s.id = opening.subject_id
                LEFT JOIN (
                    SELECT vd.subject_id,
                           SUM(vd.debit_amount) as period_debit,
                           SUM(vd.credit_amount) as period_credit
                    FROM voucher_details vd
                    INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                    WHERE fv.area_id = ?
                    AND fv.voucher_date >= ?
                    AND fv.voucher_date <= ?
                    AND fv.status IN ('已审核', '已记账')
                    GROUP BY vd.subject_id
                ) period ON s.id = period.subject_id
                WHERE s.area_id = ? AND s.is_active = 1
        
            )
            SELECT id, code, name, subject_type, balance_direction, level,
                   opening_balance, period_debit, period_credit,
                   (opening_balance + period_debit - period_credit) as ending_balance
            FROM subject_summary
            WHERE (opening_balance != 0 OR period_debit != 0 OR period_credit != 0)
            ORDER BY code
        ]
[parameters: (42, datetime.date(2025, 6, 1), 42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11), 42)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:374]
