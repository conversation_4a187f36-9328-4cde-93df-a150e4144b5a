2025-06-13 23:12:26,660 INFO: 应用启动 - PID: 4436 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-13 23:12:32,793 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-13 23:12:32,794 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-13 23:12:32,794 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-13 23:12:32,800 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-13 23:12:32,800 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-13 23:12:32,806 INFO: 周菜单 41 总共有 26 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-13 23:12:32,806 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,806 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,807 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,807 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,807 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,807 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,808 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,808 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,808 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,808 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,808 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,809 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,809 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,809 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,809 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,809 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,810 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,810 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,810 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,810 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,810 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,811 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,811 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,811 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,811 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,811 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-13 23:12:32,816 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-13 23:12:32,817 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-13 23:12:32,817 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-13 23:12:32,822 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-13 23:12:32,828 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-13 23:12:32,833 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,835 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,835 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,838 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,838 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,843 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,848 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,860 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,867 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,867 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,871 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,875 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,875 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,879 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,883 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,884 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,885 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,886 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,887 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,888 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,889 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-13 23:12:32,889 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-13 23:17:07,677 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:17:07,678 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:17:07,679 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:17:07,679 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:19:49,182 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 23:19:53,685 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 23:20:05,230 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 23:22:55,752 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:22:55,753 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:22:55,758 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:22:55,762 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:23:03,411 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:23:03,411 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:23:03,413 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:23:03,413 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:23:08,172 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:23:08,173 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:23:08,175 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:23:08,175 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:23:11,212 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:23:11,214 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:23:11,215 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:23:11,215 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:28:09,144 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:28:09,153 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:28:09,155 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:28:09,156 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:30:18,500 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:30:18,500 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:30:18,503 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:30:18,503 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:30:22,155 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:30:22,158 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:30:22,158 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:30:22,159 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:33:26,957 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:33:26,957 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:33:26,957 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:33:26,957 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:33:30,676 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:33:30,676 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:33:30,676 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:33:30,676 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:34:50,486 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:34:50,495 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:34:50,496 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:34:50,496 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:40:42,529 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:40:42,529 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:40:42,529 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:40:42,529 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:40:45,055 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:40:45,055 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:40:45,055 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:40:45,055 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:40:50,448 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:40:50,448 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:40:50,448 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:40:50,458 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:40:50,478 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:40:50,479 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:41:27,926 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:41:27,930 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:41:27,930 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:41:27,931 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:41:27,965 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:41:27,966 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:44:30,852 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 23:44:30,852 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 23:44:30,852 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 23:44:30,852 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 23:44:34,818 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:44:34,818 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:44:34,818 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:44:34,818 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:44:38,134 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:44:38,136 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:44:38,136 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:44:38,137 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:44:38,164 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:44:38,164 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
