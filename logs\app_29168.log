2025-06-11 11:02:37,563 INFO: 应用启动 - PID: 29168 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 11:02:42,004 INFO: 查询到 10 个待生成应付账款的入库单 [in D:\StudentsCMSSP\app\routes\financial\payables.py:429]
2025-06-11 11:02:42,004 INFO: 入库单: RK20250603125249, 状态: 已入库, 应付账款ID: None, 总金额: 68000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,005 INFO: 入库单: RK20250602204426, 状态: 已入库, 应付账款ID: None, 总金额: 396046.90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,005 INFO: 入库单: RK20250601021022, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,005 INFO: 入库单: RK20250601021012, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,005 INFO: 入库单: RK20250529123839, 状态: 已入库, 应付账款ID: None, 总金额: 409402.28 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,006 INFO: 入库单: RK20250528223445, 状态: 已入库, 应付账款ID: None, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,006 INFO: 入库单: RK20250528133013, 状态: 已入库, 应付账款ID: None, 总金额: 75000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,006 INFO: 入库单: RK20250527155522, 状态: 已入库, 应付账款ID: None, 总金额: 30000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,006 INFO: 入库单: RK20250526165105, 状态: 已入库, 应付账款ID: None, 总金额: 25000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,006 INFO: 入库单: RK20250525203133, 状态: 已入库, 应付账款ID: None, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 11:02:42,014 INFO: 该区域总入库单数量: 16 [in D:\StudentsCMSSP\app\routes\financial\payables.py:435]
2025-06-11 11:02:42,022 INFO: 该区域已入库的入库单数量: 10 [in D:\StudentsCMSSP\app\routes\financial\payables.py:442]
2025-06-11 11:02:45,686 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:121]
2025-06-11 11:02:45,687 INFO: 请求数据: {'stock_in_id': 93} [in D:\StudentsCMSSP\app\routes\financial\payables.py:125]
2025-06-11 11:02:45,688 INFO: 入库单ID: 93 [in D:\StudentsCMSSP\app\routes\financial\payables.py:128]
2025-06-11 11:02:45,688 INFO: 查询入库单: ID=93, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:135]
2025-06-11 11:02:45,692 INFO: 找到入库单: RK20250603125249, 状态: 已入库, 总金额: 68000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:145]
2025-06-11 11:02:45,693 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:148]
2025-06-11 11:02:45,697 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:155]
2025-06-11 11:02:45,698 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:161]
2025-06-11 11:02:45,698 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:166]
2025-06-11 11:02:45,701 INFO: 生成首个应付账款编号: AP20250611001 [in D:\StudentsCMSSP\app\routes\financial\payables.py:179]
2025-06-11 11:02:45,704 INFO: 开始查询会计科目... [in D:\StudentsCMSSP\app\routes\financial\payables.py:253]
2025-06-11 11:02:45,704 INFO: 查询原材料科目 (1201)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:255]
2025-06-11 11:02:45,710 INFO: 找到原材料科目: 原材料 (ID: 206) [in D:\StudentsCMSSP\app\routes\financial\payables.py:271]
2025-06-11 11:02:45,710 INFO: 查询应付账款科目 (2001)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 11:02:45,712 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in D:\StudentsCMSSP\app\routes\financial\payables.py:291]
2025-06-11 11:02:45,712 INFO: 准备凭证明细SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-11 11:02:45,712 INFO: 准备更新入库单SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:312]
2025-06-11 11:02:45,713 INFO: 开始执行数据库事务... [in D:\StudentsCMSSP\app\routes\financial\payables.py:320]
2025-06-11 11:02:45,713 INFO: 应付账款参数: {'payable_number': 'AP20250611001', 'area_id': 42, 'supplier_id': 28, 'stock_in_id': 93, 'purchase_order_id': 59, 'original_amount': 68000.0, 'paid_amount': 0.0, 'balance_amount': 68000.0, 'due_date': None, 'status': '未付款', 'payment_terms': None, 'invoice_number': None, 'invoice_date': None, 'invoice_amount': None, 'created_by': 34, 'notes': None} [in D:\StudentsCMSSP\app\routes\financial\payables.py:321]
2025-06-11 11:02:45,713 INFO: 财务凭证参数: {'voucher_number': 'PZ20250611001', 'voucher_date': '2025-06-11', 'area_id': 42, 'voucher_type': '入库凭证', 'summary': '入库单RK20250603125249生成应付账款', 'total_amount': 68000.0, 'status': '已审核', 'source_type': '入库单', 'source_id': 93, 'created_by': 34} [in D:\StudentsCMSSP\app\routes\financial\payables.py:322]
2025-06-11 11:02:45,714 INFO: 事务开始 [in D:\StudentsCMSSP\app\routes\financial\payables.py:325]
2025-06-11 11:02:45,714 INFO: 执行应付账款SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:328]
2025-06-11 11:02:45,717 INFO: 应付账款创建成功，ID: 2 [in D:\StudentsCMSSP\app\routes\financial\payables.py:331]
2025-06-11 11:02:45,717 INFO: 执行财务凭证SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:334]
2025-06-11 11:02:45,719 INFO: 财务凭证创建成功，ID: 22 [in D:\StudentsCMSSP\app\routes\financial\payables.py:337]
2025-06-11 11:02:45,720 INFO: 创建凭证明细... [in D:\StudentsCMSSP\app\routes\financial\payables.py:341]
2025-06-11 11:02:45,720 INFO: 借方明细参数: {'voucher_id': 22, 'line_number': 1, 'subject_id': 206, 'summary': '入库单RK20250603125249', 'debit_amount': 68000.0, 'credit_amount': 0.0} [in D:\StudentsCMSSP\app\routes\financial\payables.py:352]
2025-06-11 11:02:45,725 INFO: 借方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-11 11:02:45,725 INFO: 贷方明细参数: {'voucher_id': 22, 'line_number': 2, 'subject_id': 220, 'summary': '入库单RK20250603125249', 'debit_amount': 0.0, 'credit_amount': 68000.0} [in D:\StudentsCMSSP\app\routes\financial\payables.py:365]
2025-06-11 11:02:45,726 INFO: 贷方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:367]
2025-06-11 11:02:45,727 INFO: 更新入库单关联信息... [in D:\StudentsCMSSP\app\routes\financial\payables.py:372]
2025-06-11 11:02:45,727 INFO: 更新参数: {'payable_id': 2, 'voucher_id': 22, 'stock_in_id': 93} [in D:\StudentsCMSSP\app\routes\financial\payables.py:378]
2025-06-11 11:02:45,731 INFO: 入库单更新成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:380]
2025-06-11 11:02:45,732 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:382]
2025-06-11 11:02:45,732 INFO: === 应付账款生成成功 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:384]
2025-06-11 11:02:45,733 INFO: 应付账款ID: 2 [in D:\StudentsCMSSP\app\routes\financial\payables.py:385]
2025-06-11 11:02:45,733 INFO: 财务凭证ID: 22 [in D:\StudentsCMSSP\app\routes\financial\payables.py:386]
2025-06-11 11:04:01,993 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 11:04:01,993 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 11:04:01,994 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
2025-06-11 11:05:03,270 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 11:05:03,271 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 11:05:03,271 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
2025-06-11 11:17:25,508 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 11:17:25,509 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 11:17:25,510 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
