2025-06-12 23:27:24,617 INFO: 应用启动 - PID: 15896 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-12 23:27:32,984 INFO: 批量生成明细账请求: user_area=22, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-12 23:27:32,985 INFO: 获取有发生额的科目: area_id=22, year=2025, month=6 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-12 23:27:32,989 INFO: 找到有发生额的科目数量: 0 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
2025-06-12 23:27:59,172 INFO: 批量生成明细账请求: user_area=22, request_data={'year': 2025, 'month': 5, 'subject_ids': []} [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-12 23:27:59,173 INFO: 获取有发生额的科目: area_id=22, year=2025, month=5 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-12 23:27:59,177 INFO: 找到有发生额的科目数量: 0 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
2025-06-12 23:28:05,711 INFO: 批量生成明细账请求: user_area=22, request_data={'year': 2025, 'month': 4, 'subject_ids': []} [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-12 23:28:05,711 INFO: 获取有发生额的科目: area_id=22, year=2025, month=4 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-12 23:28:05,715 INFO: 找到有发生额的科目数量: 0 [in D:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
