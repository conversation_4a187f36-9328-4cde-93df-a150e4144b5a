2025-06-11 02:54:09,379 INFO: 应用启动 - PID: 22452 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 03:28:06,280 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 03:28:06,299 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 03:37:18,198 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 3, 37, 18), datetime.datetime(2025, 6, 11, 3, 37, 18))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 03:40:08,852 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 3, 40, 8), datetime.datetime(2025, 6, 11, 3, 40, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 03:42:25,211 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 3, 42, 25), datetime.datetime(2025, 6, 11, 3, 42, 25))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 03:43:37,442 ERROR: 财务凭证生成失败: 'str' object has no attribute 'strftime', 入库单ID: 77 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1588]
2025-06-11 03:43:37,443 ERROR: 从入库单生成凭证失败: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1265]
2025-06-11 03:43:59,296 ERROR: 财务凭证生成失败: 'str' object has no attribute 'strftime', 入库单ID: 77 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1588]
2025-06-11 03:43:59,296 ERROR: 从入库单生成凭证失败: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1265]
2025-06-11 03:44:25,587 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 3, 44, 25), datetime.datetime(2025, 6, 11, 3, 44, 25))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 08:52:31,163 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 8, 52, 31), datetime.datetime(2025, 6, 11, 8, 52, 31))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 08:55:28,632 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 8, 55, 28), datetime.datetime(2025, 6, 11, 8, 55, 28))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
