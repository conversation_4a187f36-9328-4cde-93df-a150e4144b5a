{% extends 'base.html' %}
{% from 'macros/purchase_order_status.html' import render_status_timeline, render_status_progress, get_unified_status %}

{% block title %}采购订单详情 - {{ order.order_number }} - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 采购订单详情页面专用样式 - 基于列表页面设计 */

/* 订单摘要信息样式 */
.order-summary-info h5 {
    color: #2c3e50;
    font-weight: 600;
}

.order-summary-info small {
    font-size: 0.8rem;
}

/* 表格优化 - 学习列表页面的紧凑设计 */
.table-compact {
    font-size: 0.875rem;
    table-layout: fixed;
    width: 100%;
}

.table-compact th {
    font-weight: normal;
    padding: 0.5rem;
    border-top: none;
    background-color: #f8f9fa;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-compact td {
    padding: 0.5rem;
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 列宽优化 - 按实际需求重新分配 */
.sequence-column {
    width: 15% !important;
    text-align: center !important;
    min-width: 80px;
}

.ingredient-name-column {
    width: 25% !important;
    min-width: 120px;
}

.quantity-column {
    width: 12% !important;
    text-align: center !important;
    min-width: 80px;
}

.unit-column {
    width: 12% !important;
    text-align: center !important;
    min-width: 70px;
}

.supplier-column {
    width: 30% !important;
    text-align: center !important;
    min-width: 130px;
}

.status-info-column {
    width: 12% !important;
    text-align: center !important;
    min-width: 80px;
}

/* 状态信息优化 */
.status-column-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
}

/* 订单状态卡片优化 */
.order-status-card {
    margin-bottom: 1rem;
}

.order-status-card .card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.order-status-card .status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
}

.order-status-card .status-info h6 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.order-status-card .status-info small {
    line-height: 1.4;
}

/* 时间线优化 */
.purchase-order-timeline {
    position: relative;
    padding-left: 1.5rem;
}

.purchase-order-timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.purchase-order-timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
    padding-left: 1rem;
}

.purchase-order-timeline-item::before {
    content: '';
    position: absolute;
    left: -0.375rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.purchase-order-timeline-item.pending::before {
    background-color: #ffc107;
}

.purchase-order-timeline-title {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.purchase-order-timeline-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 操作按钮组优化 - 学习列表页面的按钮设计 */
.btn-action-group .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-action-group .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}

/* 卡片标题优化 */
.card-header h5 {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

/* 信息显示优化 */
.info-row {
    margin-bottom: 0.75rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.info-value {
    color: #6c757d;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .order-status-card .status-icon {
        width: 50px;
        height: 50px;
    }

    .order-status-card .status-icon i {
        font-size: 1.5rem !important;
    }

    .btn-action-group {
        text-align: center;
    }

    .btn-action-group .btn {
        margin: 0.25rem;
        font-size: 0.8rem;
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- 订单信息提示栏 -->
    <div class="alert alert-info mb-4" role="alert">
        <i class="fas fa-info-circle"></i>
        <strong>提示：</strong>此页面显示采购订单的详细信息和处理状态。您可以查看订单明细、跟踪处理进度，并执行相应的操作。
    </div>

    <!-- 页面操作按钮 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="order-summary-info">
                <h5 class="mb-1">{{ order.order_number }}</h5>
                <small class="text-muted">创建时间：{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</small>
            </div>
        </div>
        <div class="col-md-4">
            <!-- 移动端按钮 -->
            <div class="btn-action-group d-md-none">
                <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary btn-block">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}"
                   class="btn btn-outline-primary btn-block"
                   target="_blank">
                    <i class="fas fa-print"></i> 打印订单
                </a>
            </div>

            <!-- 桌面端按钮 -->
            <div class="d-none d-md-block text-right">
                <div class="btn-group">
                    <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}"
                       class="btn btn-outline-primary btn-sm"
                       target="_blank">
                        <i class="fas fa-print"></i> 打印订单
                    </a>

                    <!-- 管理员操作 -->
                    {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                            <i class="fas fa-cog"></i> 管理
                        </button>
                        <div class="dropdown-menu dropdown-menu-right">
                            {% if not order.has_stock_in %}
                            <button class="dropdown-item delete-btn" data-id="{{ order.id }}">
                                <i class="fas fa-trash-alt text-danger"></i> 删除订单
                            </button>
                            {% endif %}
                            <div class="dropdown-divider"></div>
                            <h6 class="dropdown-header">当前状态</h6>
                            <span class="dropdown-item-text">
                                <small class="text-muted">{{ order.status_display }}</small>
                            </span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 订单信息 -->
        <div class="col-md-8">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-row">
                                <div class="info-label">订单编号</div>
                                <div class="info-value">{{ order.order_number }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">采购区域</div>
                                <div class="info-value">{{ order.area_name if order.area_name else '-' }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-row">
                                <div class="info-label">预计送货日期</div>
                                <div class="info-value">{{ order.expected_delivery_date|format_datetime('%Y-%m-%d') if order.expected_delivery_date else '-' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">实际送货日期</div>
                                <div class="info-value">{{ order.delivery_date|format_datetime('%Y-%m-%d') if order.delivery_date else '-' }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-row">
                                <div class="info-label">订单状态</div>
                                <div class="info-value">
                                    {% if order.status == '待确认' %}
                                    <span class="purchase-order-status status-pending">待确认</span>
                                    {% elif order.status == '已确认' %}
                                    <span class="purchase-order-status status-confirmed">已确认</span>
                                    {% elif order.status == '准备入库' %}
                                    <span class="purchase-order-status status-delivered">准备入库</span>
                                    {% elif order.status == '已取消' %}
                                    <span class="purchase-order-status status-cancelled">已取消</span>
                                    {% else %}
                                    <span class="purchase-order-status">{{ order.get_status_display() }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">处理状态</div>
                                <div class="info-value">
                                    {% if order.status_info %}
                                        <!-- 入库状态 -->
                                        {% if order.status_info.has_stock_in %}
                                            {% set stock_in_status = order.status_info.stock_in_status %}
                                            {% if stock_in_status == '已入库' %}
                                                <span class="badge badge-success">{{ stock_in_status }}</span>
                                            {% elif stock_in_status == '已审核' %}
                                                <span class="badge badge-info">{{ stock_in_status }}</span>
                                            {% elif stock_in_status == '待审核' %}
                                                <span class="badge badge-warning">{{ stock_in_status }}</span>
                                            {% elif stock_in_status == '已取消' %}
                                                <span class="badge badge-danger">{{ stock_in_status }}</span>
                                            {% else %}
                                                <span class="badge badge-info">{{ stock_in_status }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge badge-secondary">未入库</span>
                                        {% endif %}

                                        <!-- 消耗状态 -->
                                        {% if order.status_info.consumption_status != '未消耗' %}
                                            <span class="badge {% if order.status_info.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %} ml-1">
                                                {{ order.status_info.consumption_status }}
                                            </span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消耗详情 -->
                    {% if order.status_info and order.status_info.consumption_details %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="info-row">
                                <div class="info-label">消耗餐次</div>
                                <div class="info-value text-muted">
                                    {% for detail in order.status_info.consumption_details %}
                                        {{ detail.consumption_date|format_datetime('%Y-%m-%d') }} {{ detail.meal_type }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% if order.notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>备注：</strong>{{ order.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">采购明细</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-compact">
                            <thead>
                                <tr>
                                    <th class="sequence-column">序号</th>
                                    <th class="ingredient-name-column">食材名称</th>
                                    <th class="quantity-column">采购量</th>
                                    <th class="unit-column">单位</th>
                                    <th class="supplier-column">供应商</th>
                                    <th class="status-info-column">状态信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order['order_items'] %}
                                <tr class="{% if order.status_info and order.status_info.items_status %}
                                    {% for item_status in order.status_info.items_status %}
                                        {% if item_status.ingredient_name == item.ingredient_name %}
                                            {% if item_status.consumption_status == '已消耗' %}table-light{% elif item_status.consumption_status == '部分消耗' %}table-warning{% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}">
                                    <td class="sequence-column">{{ loop.index }}</td>
                                    <td class="ingredient-name-column">{{ item.ingredient_name }}</td>
                                    <td class="quantity-column text-center">{{ item.quantity }}</td>
                                    <td class="unit-column text-center">{{ item.unit }}</td>
                                    <td class="supplier-column text-center">{{ item.supplier_name if item.supplier_name else '自购' }}</td>
                                    <td class="status-info-column">
                                        <div class="status-column-content">
                                            <!-- 入库状态 -->
                                            {% if order.status_info and order.status_info.has_stock_in %}
                                                {% set stock_in_status = order.status_info.stock_in_status %}
                                                {% if stock_in_status == '已入库' %}
                                                    <span class="badge badge-success">{{ stock_in_status }}</span>
                                                {% elif stock_in_status == '已审核' %}
                                                    <span class="badge badge-info">{{ stock_in_status }}</span>
                                                {% elif stock_in_status == '待审核' %}
                                                    <span class="badge badge-warning">{{ stock_in_status }}</span>
                                                {% elif stock_in_status == '已取消' %}
                                                    <span class="badge badge-danger">{{ stock_in_status }}</span>
                                                {% else %}
                                                    <span class="badge badge-info">{{ stock_in_status }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge badge-secondary">未入库</span>
                                            {% endif %}

                                            <!-- 消耗状态 -->
                                            {% if order.status_info and order.status_info.items_status %}
                                                {% for item_status in order.status_info.items_status %}
                                                    {% if item_status.ingredient_name == item.ingredient_name %}
                                                        {% if item_status.consumption_status != '未消耗' %}
                                                            <span class="badge {% if item_status.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %}">
                                                                {{ item_status.consumption_status }}
                                                            </span>

                                                            <!-- 消耗餐次详情 -->
                                                            {% if item_status.consumption_meals %}
                                                                <small class="text-muted d-block mt-1" style="font-size: 0.7em;">
                                                                    {% for meal in item_status.consumption_meals %}
                                                                        {{ meal.consumption_date|format_datetime('%m-%d') }} {{ meal.meal_type }}{% if not loop.last %}, {% endif %}
                                                                    {% endfor %}
                                                                </small>
                                                            {% endif %}
                                                        {% endif %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>



            <!-- 订单状态卡片 -->
            <div class="order-status-card w-100">
                <div class="card border-left-primary">
                    <div class="card-body py-3">
                        <div class="d-flex align-items-center">
                            <div class="status-icon mr-3">
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    <i class="fas fa-check-circle text-success fa-2x"></i>
                                {% elif order.status == '已取消' %}
                                    <i class="fas fa-times-circle text-danger fa-2x"></i>
                                {% elif order.status == '准备入库' %}
                                    <i class="fas fa-dolly text-primary fa-2x"></i>
                                {% elif order.status == '已确认' %}
                                    <i class="fas fa-truck text-info fa-2x"></i>
                                {% else %}
                                    <i class="fas fa-clock text-warning fa-2x"></i>
                                {% endif %}
                            </div>
                            <div class="status-info">
                                <h6 class="mb-1">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        已完成入库
                                    {% else %}
                                        {{ order.status_display }}
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        订单流程已完成，食材已入库
                                    {% elif order.status == '已取消' %}
                                        订单已取消
                                    {% elif order.status == '准备入库' %}
                                        食材已送达，等待创建入库单
                                    {% elif order.status == '已确认' %}
                                        订单已确认，等待送达
                                    {% else %}
                                        订单等待确认
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作区域 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="purchase-order-actions">
                        <!-- 根据订单状态显示相应的操作按钮 -->
                        {% set stock_in_status = order.status_info.stock_in_status if order.status_info else None %}
                        {% set has_stock_in = order.status_info.has_stock_in if order.status_info else False %}

                        {% if order.status == '待确认' %}
                            <!-- 待确认状态：可以确认或取消 -->
                            <button type="button" class="btn btn-success btn-sm btn-block confirm-btn" data-id="{{ order.id }}">
                                <i class="fas fa-check"></i> 确认订单
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm btn-block cancel-btn" data-id="{{ order.id }}">
                                <i class="fas fa-times"></i> 取消订单
                            </button>
                        {% elif order.status == '已确认' %}
                            <!-- 已确认状态：可以标记送达 -->
                            <button type="button" class="btn btn-info btn-sm btn-block deliver-btn" data-id="{{ order.id }}">
                                <i class="fas fa-truck"></i> 标记送达
                            </button>
                            {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                            <button type="button" class="btn btn-outline-danger btn-sm btn-block cancel-btn" data-id="{{ order.id }}">
                                <i class="fas fa-times"></i> 取消订单
                            </button>
                            {% endif %}
                        {% elif order.status == '准备入库' %}
                            <!-- 准备入库状态：可以创建入库单或查看入库单 -->
                            {% if not has_stock_in %}
                                <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-success btn-sm btn-block">
                                    <i class="fas fa-dolly"></i> 创建入库单
                                </a>
                            {% else %}
                                <a href="{{ url_for('stock_in.view_details', id=order.status_info.stock_in_id) }}" class="btn btn-info btn-sm btn-block">
                                    <i class="fas fa-clipboard-list"></i> 查看入库单
                                </a>
                            {% endif %}
                        {% elif order.status == '已取消' %}
                            <!-- 已取消状态：只能查看和删除 -->
                            {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                            <button type="button" class="btn btn-outline-danger btn-sm btn-block delete-btn" data-id="{{ order.id }}">
                                <i class="fas fa-trash"></i> 删除订单
                            </button>
                            {% endif %}
                        {% endif %}

                        <!-- 入库完成后的状态显示 -->
                        {% if has_stock_in and stock_in_status == '已入库' %}
                            <div class="alert alert-success text-center mb-0">
                                <i class="fas fa-check-circle"></i> 订单已完成入库
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？标记送达后，订单将进入"准备入库"状态，您可以创建入库单。</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 状态更新函数
    function updateOrderStatus(newStatus) {
        // 更新状态显示
        const statusElement = $('.purchase-order-status');
        statusElement.removeClass('status-pending status-confirmed status-delivered status-cancelled');

        let statusClass = '';
        let statusText = newStatus;

        switch(newStatus) {
            case '待确认':
                statusClass = 'status-pending';
                break;
            case '已确认':
                statusClass = 'status-confirmed';
                break;
            case '准备入库':
                statusClass = 'status-delivered';
                break;
            case '已取消':
                statusClass = 'status-cancelled';
                break;
        }

        statusElement.addClass(statusClass).text(statusText);

        // 更新操作按钮的可见性
        updateActionButtons(newStatus);

        // 更新流程步骤
        updateProcessSteps(newStatus);
    }

    function updateActionButtons(status) {
        $('.action-buttons .btn').hide();

        switch(status) {
            case '待确认':
                $('.confirm-btn, .cancel-btn, .delete-btn').show();
                break;
            case '已确认':
                $('.deliver-btn, .cancel-btn').show();
                break;
            case '准备入库':
                $('.create-stock-in-btn').show();
                break;
            case '已取消':
                $('.delete-btn').show();
                break;
        }
    }

    function updateProcessSteps(status) {
        $('.compact-process-step').removeClass('active completed');

        switch(status) {
            case '待确认':
                $('.compact-process-step[data-step="pending"]').addClass('active');
                break;
            case '已确认':
                $('.compact-process-step[data-step="pending"]').addClass('completed');
                $('.compact-process-step[data-step="confirmed"]').addClass('active');
                break;
            case '准备入库':
                $('.compact-process-step[data-step="pending"], .compact-process-step[data-step="confirmed"]').addClass('completed');
                $('.compact-process-step[data-step="delivered"]').addClass('active');
                break;
            case '已取消':
                $('.compact-process-step').removeClass('active completed');
                $('.compact-process-step[data-step="cancelled"]').addClass('active');
                break;
        }
    }

    function showSuccessMessage(message) {
        // 显示成功消息
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        $('.card-body').first().prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert-success').alert('close');
        }, 3000);
    }



    // 确认订单
    $('.confirm-btn').click(function() {
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.confirm_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('已确认');
                    showSuccessMessage(response.message);
                    $('#confirmModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('确认订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            alert('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.cancel_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('已取消');
                    showSuccessMessage(response.message);
                    $('#cancelModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('取消订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
            }
        });
    });

    // 标记送达
    $('.deliver-btn').click(function() {
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.deliver_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('准备入库');
                    showSuccessMessage(response.message);
                    $('#deliverModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('标记送达失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.delete_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 删除成功后跳转到列表页
                    window.location.href = '{{ url_for("purchase_order.index") }}';
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('删除订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });
});
</script>
{% endblock %}
