2025-06-12 23:52:48,367 INFO: 应用启动 - PID: 11484 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-13 00:01:16,437 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:01:44,621 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:01:44,709 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:02:14,597 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:02:14,611 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:03:05,012 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:03:05,028 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:04:11,053 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:04:11,064 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:15:49,291 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:15:49,366 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:18:15,535 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:18:15,552 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:18:24,030 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-13 00:19:23,369 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:19:23,383 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:19:43,367 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:19:43,372 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:20:16,788 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:20:16,800 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:20:25,891 INFO: 自动填充菜单数据: 早餐=None, 午餐=None, 晚餐=None [in C:\StudentsCMSSP\app\services\daily_management_service.py:341]
2025-06-13 00:20:35,577 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:20:35,588 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:21:55,397 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:21:55,406 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:22:13,235 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:22:13,241 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:22:27,862 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:22:27,887 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:26:10,214 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:26:10,222 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:26:17,319 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:26:17,331 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:26:25,397 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:26:25,408 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:26:55,052 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:26:55,052 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 00:27:01,561 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 00:27:01,574 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 01:05:43,514 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 01:05:43,521 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 01:21:44,419 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 01:21:44,433 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 01:22:09,716 INFO: 复制食谱 - 原食谱ID: 349, 名称: 韭菜炒藕丝 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:22:09,716 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:22:09,716 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:22:17,753 INFO: 复制食谱 - 原食谱ID: 348, 名称: 韭菜桃仁 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:22:17,753 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:22:17,753 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:22:46,619 INFO: 复制食谱 - 原食谱ID: 341, 名称: 长豇豆烧茄子 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:22:46,619 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:22:46,619 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:23:02,884 INFO: 复制食谱 - 原食谱ID: 332, 名称: 酸辣土豆丝 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:23:02,884 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:23:02,888 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:23:53,419 INFO: 复制食谱 - 原食谱ID: 274, 名称: 湘菜手撕包菜 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:23:53,419 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:23:53,419 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:24:11,013 INFO: 复制食谱 - 原食谱ID: 262, 名称: 洞庭蚕豆 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:24:11,013 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:24:11,013 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:24:23,591 INFO: 复制食谱 - 原食谱ID: 254, 名称: 椒香青豌豆 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:24:23,591 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:24:23,591 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:24:32,796 INFO: 复制食谱 - 原食谱ID: 247, 名称: 手撕包菜 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:24:32,796 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:24:32,796 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:25:13,672 INFO: 复制食谱 - 原食谱ID: 223, 名称: 大碗长豆角 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:25:13,672 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:25:13,672 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:25:22,642 INFO: 复制食谱 - 原食谱ID: 224, 名称: 大蒜辣椒炒油渣 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:25:22,642 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:25:22,647 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:25:24,376 INFO: 复制食谱 - 原食谱ID: 222, 名称: 大碗冬瓜 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:25:24,376 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:25:24,376 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:25:38,627 INFO: 复制食谱 - 原食谱ID: 213, 名称: 剁椒笋片 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:25:38,627 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:25:38,638 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:25:46,474 INFO: 复制食谱 - 原食谱ID: 211, 名称: 刨花青笋 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:25:46,474 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:25:46,474 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:26:10,451 INFO: 复制食谱 - 原食谱ID: 180, 名称: 炒包菜 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:26:10,451 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:26:10,451 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:26:15,201 INFO: 复制食谱 - 原食谱ID: 175, 名称: 红薯米饭 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:26:15,201 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:26:15,206 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:26:21,028 INFO: 复制食谱 - 原食谱ID: 174, 名称: 炒大白菜 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:26:21,028 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:26:21,028 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:26:31,956 INFO: 复制食谱 - 原食谱ID: 171, 名称: 黄豆红烧肉 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:26:31,957 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:26:31,958 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:26:46,465 INFO: 复制食谱 - 原食谱ID: 161, 名称: 韭菜炒豆芽 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:26:46,465 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:26:46,465 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:27:14,234 INFO: 复制食谱 - 原食谱ID: 91, 名称: 爽口面条 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:27:14,234 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:27:14,234 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:27:24,624 INFO: 复制食谱 - 原食谱ID: 80, 名称: 豆腐汤 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:27:24,624 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:27:24,624 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:28:02,839 INFO: 复制食谱 - 原食谱ID: 20, 名称: 蒸蛋羹 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:28:02,839 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:28:02,839 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:28:13,904 INFO: 复制食谱 - 原食谱ID: 6, 名称: 麻婆豆腐 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:28:13,904 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:28:13,904 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:28:21,350 INFO: 复制食谱 - 原食谱ID: 2, 名称: 红烧肉 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:28:21,350 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:28:21,357 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:29:17,140 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-13 01:30:07,279 INFO: 复制食谱 - 原食谱ID: 168, 名称: 西红柿炒蛋 [in C:\StudentsCMSSP\app\routes\recipe.py:790]
2025-06-13 01:30:07,279 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\StudentsCMSSP\app\routes\recipe.py:791]
2025-06-13 01:30:07,279 INFO: 用户区域信息 - user_area: 海淀区中关村第一小学, ID: 44 [in C:\StudentsCMSSP\app\routes\recipe.py:795]
2025-06-13 01:31:16,278 ERROR: 创建食谱失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]不能将值 NULL 插入列 'category'，表 'StudentsCMSSP.dbo.recipes'；列不允许有 Null 值。INSERT 失败。 (515) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)")
[SQL: 
            INSERT INTO recipes
            (name, category, category_id, area_id, meal_type, main_image, description, status, is_user_defined, is_global, priority, created_by)
            OUTPUT inserted.id
            VALUES
            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ]
[parameters: ('包子', None, None, 44, '', None, '', 1, 0, 0, 0, 38)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\StudentsCMSSP\app\routes\recipe.py:169]
2025-06-13 01:33:41,653 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-09"}' [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-13 01:33:41,653 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-09 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-13 01:33:41,653 INFO: 检查用户权限: user_id=38, area_id=44 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-13 01:33:41,653 INFO: 用户角色: ['学校管理员'] [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-13 01:33:41,653 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-13 01:33:41,653 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=38 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-13 01:33:41,653 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=38 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-13 01:33:41,653 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-13 01:33:41,653 INFO: 计算的周结束日期: 2025-06-15 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-13 01:33:41,663 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-09 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-13 01:33:41,663 INFO: 获取周菜单: area_id=44, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-13 01:33:41,663 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-09 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-13 01:33:41,663 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-09 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-13 01:33:41,670 INFO: 主查询未找到菜单: area_id=44, week_start=2025-06-09 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-13 01:33:41,671 INFO: 使用日期字符串: week_start_str=2025-06-09, week_end_str=2025-06-15 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-13 01:33:41,671 INFO: 准备执行SQL创建菜单 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-13 01:33:41,672 INFO: SQL参数: {'area_id': '44', 'week_start_str': '2025-06-09', 'week_end_str': '2025-06-15', 'status': '计划中', 'created_by': 38} [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-13 01:33:41,673 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-13 01:33:41,681 INFO: SQL执行成功，获取到ID: 41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-13 01:33:41,682 INFO: 检查数据库连接状态 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-13 01:33:41,684 INFO: 数据库连接正常 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-13 01:33:41,686 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-13 01:33:41,687 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-13 01:33:41,699 INFO: 验证成功: 菜单已创建 ID=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-13 01:33:41,700 INFO: 周菜单创建成功: id=41 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-13 01:33:41,703 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 41, 'status': '计划中'} [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-13 01:34:15,398 ERROR: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:508]
2025-06-13 01:34:15,398 ERROR: 周菜单操作错误: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\utils\decorators.py:112]
2025-06-13 01:34:49,179 ERROR: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:508]
2025-06-13 01:34:49,179 ERROR: 周菜单操作错误: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\utils\decorators.py:112]
2025-06-13 01:39:25,545 ERROR: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:508]
2025-06-13 01:39:25,545 ERROR: 周菜单操作错误: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\utils\decorators.py:112]
2025-06-13 01:39:54,627 ERROR: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:508]
2025-06-13 01:39:54,627 ERROR: 周菜单操作错误: 保存周菜单失败: No module named 'app.weekly_menu_recipes_temp' [in C:\StudentsCMSSP\app\utils\decorators.py:112]
