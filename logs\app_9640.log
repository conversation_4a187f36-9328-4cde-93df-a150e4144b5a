2025-06-13 23:46:48,792 INFO: 应用启动 - PID: 9640 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-13 23:46:58,997 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:46:59,003 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:46:59,004 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:46:59,006 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:46:59,199 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:46:59,200 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:48:03,089 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-13', 'meal_types': ['早餐', '午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-13 23:48:03,105 INFO: 查询日期: 2025-06-13, 区域: 44, 餐次: ['早餐', '午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-13 23:48:03,110 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:48:03,116 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:48:03,154 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-13 23:48:03,161 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-13 23:48:35,866 ERROR: 检查订单更新失败: 'str' object has no attribute 'isoformat' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2041]
