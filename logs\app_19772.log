2025-06-11 12:20:13,454 INFO: 应用启动 - PID: 19772 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 12:20:29,565 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 12:20:29,588 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 12:20:37,696 INFO: 查询到 9 个待生成应付账款的入库单 [in D:\StudentsCMSSP\app\routes\financial\payables.py:429]
2025-06-11 12:20:37,696 INFO: 入库单: RK20250602204426, 状态: 已入库, 应付账款ID: None, 总金额: 396046.90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,697 INFO: 入库单: RK20250601021022, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250601021012, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250529123839, 状态: 已入库, 应付账款ID: None, 总金额: 409402.28 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250528223445, 状态: 已入库, 应付账款ID: None, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250528133013, 状态: 已入库, 应付账款ID: None, 总金额: 75000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250527155522, 状态: 已入库, 应付账款ID: None, 总金额: 30000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250526165105, 状态: 已入库, 应付账款ID: None, 总金额: 25000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,698 INFO: 入库单: RK20250525203133, 状态: 已入库, 应付账款ID: None, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:20:37,704 INFO: 该区域总入库单数量: 16 [in D:\StudentsCMSSP\app\routes\financial\payables.py:435]
2025-06-11 12:20:37,714 INFO: 该区域已入库的入库单数量: 10 [in D:\StudentsCMSSP\app\routes\financial\payables.py:442]
2025-06-11 12:20:57,435 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 12:20:57,435 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 12:20:57,436 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
2025-06-11 12:21:08,066 INFO: 查询到 9 个待生成应付账款的入库单 [in D:\StudentsCMSSP\app\routes\financial\payables.py:429]
2025-06-11 12:21:08,067 INFO: 入库单: RK20250602204426, 状态: 已入库, 应付账款ID: None, 总金额: 396046.90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,067 INFO: 入库单: RK20250601021022, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,067 INFO: 入库单: RK20250601021012, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,067 INFO: 入库单: RK20250529123839, 状态: 已入库, 应付账款ID: None, 总金额: 409402.28 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,067 INFO: 入库单: RK20250528223445, 状态: 已入库, 应付账款ID: None, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,068 INFO: 入库单: RK20250528133013, 状态: 已入库, 应付账款ID: None, 总金额: 75000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,068 INFO: 入库单: RK20250527155522, 状态: 已入库, 应付账款ID: None, 总金额: 30000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,068 INFO: 入库单: RK20250526165105, 状态: 已入库, 应付账款ID: None, 总金额: 25000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,068 INFO: 入库单: RK20250525203133, 状态: 已入库, 应付账款ID: None, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:08,069 INFO: 该区域总入库单数量: 16 [in D:\StudentsCMSSP\app\routes\financial\payables.py:435]
2025-06-11 12:21:08,070 INFO: 该区域已入库的入库单数量: 10 [in D:\StudentsCMSSP\app\routes\financial\payables.py:442]
2025-06-11 12:21:15,102 INFO: === 开始生成应付账款 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:121]
2025-06-11 12:21:15,103 INFO: 请求数据: {'stock_in_id': 91} [in D:\StudentsCMSSP\app\routes\financial\payables.py:125]
2025-06-11 12:21:15,103 INFO: 入库单ID: 91 [in D:\StudentsCMSSP\app\routes\financial\payables.py:128]
2025-06-11 12:21:15,103 INFO: 查询入库单: ID=91, area_id=42 [in D:\StudentsCMSSP\app\routes\financial\payables.py:135]
2025-06-11 12:21:15,107 INFO: 找到入库单: RK20250602204426, 状态: 已入库, 总金额: 396046.90 [in D:\StudentsCMSSP\app\routes\financial\payables.py:145]
2025-06-11 12:21:15,107 INFO: 检查是否已生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:148]
2025-06-11 12:21:15,112 INFO: 检查入库单状态: 已入库 [in D:\StudentsCMSSP\app\routes\financial\payables.py:155]
2025-06-11 12:21:15,112 INFO: 开始生成应付账款... [in D:\StudentsCMSSP\app\routes\financial\payables.py:161]
2025-06-11 12:21:15,113 INFO: 应付账款编号前缀: AP20250611 [in D:\StudentsCMSSP\app\routes\financial\payables.py:166]
2025-06-11 12:21:15,118 INFO: 基于最后编号 AP20250611001 生成新编号: AP20250611002 [in D:\StudentsCMSSP\app\routes\financial\payables.py:176]
2025-06-11 12:21:15,127 INFO: 开始查询会计科目... [in D:\StudentsCMSSP\app\routes\financial\payables.py:253]
2025-06-11 12:21:15,127 INFO: 查询原材料科目 (1201)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:255]
2025-06-11 12:21:15,133 INFO: 找到原材料科目: 原材料 (ID: 206) [in D:\StudentsCMSSP\app\routes\financial\payables.py:271]
2025-06-11 12:21:15,134 INFO: 查询应付账款科目 (2001)... [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-11 12:21:15,135 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in D:\StudentsCMSSP\app\routes\financial\payables.py:291]
2025-06-11 12:21:15,135 INFO: 准备凭证明细SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-11 12:21:15,135 INFO: 准备更新入库单SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:312]
2025-06-11 12:21:15,135 INFO: 开始执行数据库事务... [in D:\StudentsCMSSP\app\routes\financial\payables.py:320]
2025-06-11 12:21:15,135 INFO: 应付账款参数: {'payable_number': 'AP20250611002', 'area_id': 42, 'supplier_id': 28, 'stock_in_id': 91, 'purchase_order_id': 56, 'original_amount': 396046.9, 'paid_amount': 0.0, 'balance_amount': 396046.9, 'due_date': None, 'status': '未付款', 'payment_terms': None, 'invoice_number': None, 'invoice_date': None, 'invoice_amount': None, 'created_by': 34, 'notes': None} [in D:\StudentsCMSSP\app\routes\financial\payables.py:321]
2025-06-11 12:21:15,136 INFO: 财务凭证参数: {'voucher_number': 'PZ20250611002', 'voucher_date': '2025-06-11', 'area_id': 42, 'voucher_type': '入库凭证', 'summary': '入库单RK20250602204426生成应付账款', 'total_amount': 396046.9, 'status': '已审核', 'source_type': '入库单', 'source_id': 91, 'created_by': 34} [in D:\StudentsCMSSP\app\routes\financial\payables.py:322]
2025-06-11 12:21:15,136 INFO: 事务开始 [in D:\StudentsCMSSP\app\routes\financial\payables.py:325]
2025-06-11 12:21:15,136 INFO: 执行应付账款SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:328]
2025-06-11 12:21:15,141 INFO: 应付账款创建成功，ID: 3 [in D:\StudentsCMSSP\app\routes\financial\payables.py:331]
2025-06-11 12:21:15,141 INFO: 执行财务凭证SQL... [in D:\StudentsCMSSP\app\routes\financial\payables.py:334]
2025-06-11 12:21:15,145 INFO: 财务凭证创建成功，ID: 23 [in D:\StudentsCMSSP\app\routes\financial\payables.py:337]
2025-06-11 12:21:15,145 INFO: 创建凭证明细... [in D:\StudentsCMSSP\app\routes\financial\payables.py:341]
2025-06-11 12:21:15,145 INFO: 借方明细参数: {'voucher_id': 23, 'line_number': 1, 'subject_id': 206, 'summary': '入库单RK20250602204426', 'debit_amount': 396046.9, 'credit_amount': 0.0} [in D:\StudentsCMSSP\app\routes\financial\payables.py:352]
2025-06-11 12:21:15,156 INFO: 借方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-11 12:21:15,157 INFO: 贷方明细参数: {'voucher_id': 23, 'line_number': 2, 'subject_id': 220, 'summary': '入库单RK20250602204426', 'debit_amount': 0.0, 'credit_amount': 396046.9} [in D:\StudentsCMSSP\app\routes\financial\payables.py:365]
2025-06-11 12:21:15,158 INFO: 贷方明细创建成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:367]
2025-06-11 12:21:15,158 INFO: 更新入库单关联信息... [in D:\StudentsCMSSP\app\routes\financial\payables.py:372]
2025-06-11 12:21:15,158 INFO: 更新参数: {'payable_id': 3, 'voucher_id': 23, 'stock_in_id': 91} [in D:\StudentsCMSSP\app\routes\financial\payables.py:378]
2025-06-11 12:21:15,161 INFO: 入库单更新成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:380]
2025-06-11 12:21:15,162 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\routes\financial\payables.py:382]
2025-06-11 12:21:15,163 INFO: === 应付账款生成成功 === [in D:\StudentsCMSSP\app\routes\financial\payables.py:384]
2025-06-11 12:21:15,163 INFO: 应付账款ID: 3 [in D:\StudentsCMSSP\app\routes\financial\payables.py:385]
2025-06-11 12:21:15,163 INFO: 财务凭证ID: 23 [in D:\StudentsCMSSP\app\routes\financial\payables.py:386]
2025-06-11 12:21:16,485 INFO: 查询到 8 个待生成应付账款的入库单 [in D:\StudentsCMSSP\app\routes\financial\payables.py:429]
2025-06-11 12:21:16,485 INFO: 入库单: RK20250601021022, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,485 INFO: 入库单: RK20250601021012, 状态: 已入库, 应付账款ID: None, 总金额: 335.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,485 INFO: 入库单: RK20250529123839, 状态: 已入库, 应付账款ID: None, 总金额: 409402.28 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,485 INFO: 入库单: RK20250528223445, 状态: 已入库, 应付账款ID: None, 总金额: 286000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,486 INFO: 入库单: RK20250528133013, 状态: 已入库, 应付账款ID: None, 总金额: 75000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,486 INFO: 入库单: RK20250527155522, 状态: 已入库, 应付账款ID: None, 总金额: 30000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,486 INFO: 入库单: RK20250526165105, 状态: 已入库, 应付账款ID: None, 总金额: 25000.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,486 INFO: 入库单: RK20250525203133, 状态: 已入库, 应付账款ID: None, 总金额: 107200.00 [in D:\StudentsCMSSP\app\routes\financial\payables.py:431]
2025-06-11 12:21:16,487 INFO: 该区域总入库单数量: 16 [in D:\StudentsCMSSP\app\routes\financial\payables.py:435]
2025-06-11 12:21:16,488 INFO: 该区域已入库的入库单数量: 10 [in D:\StudentsCMSSP\app\routes\financial\payables.py:442]
2025-06-11 12:21:19,821 INFO: 用户 18373062333 访问应付账款页面 [in D:\StudentsCMSSP\app\routes\financial\payables.py:25]
2025-06-11 12:21:19,822 INFO: 用户区域: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\financial\payables.py:26]
2025-06-11 12:21:19,822 INFO: 用户权限: 1 [in D:\StudentsCMSSP\app\routes\financial\payables.py:27]
2025-06-11 12:21:28,024 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 12:21:28,036 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
