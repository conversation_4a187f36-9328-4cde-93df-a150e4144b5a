2025-06-11 14:00:46,214 INFO: 应用启动 - PID: 5828 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 14:01:14,902 ERROR: 获取资产负债表数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            WITH subject_balances AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id,
                    ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) as balance
                FROM accounting_subjects s
                LEFT JOIN voucher_details vd ON s.id = vd.subject_id
                LEFT JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE s.area_id = ?
                AND s.is_active = 1
                AND (fv.voucher_date IS NULL OR fv.voucher_date <= ?)
                AND (fv.status IS NULL OR fv.status IN ('已审核', '已记账'))
                GROUP BY s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id
                HAVING ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) != 0
            )
            SELECT * FROM subject_balances
            WHERE subject_type IN ('资产', '负债', '所有者权益')
            ORDER BY subject_type, code
        ]
[parameters: (42, datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:218]
