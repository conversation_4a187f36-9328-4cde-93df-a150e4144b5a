2025-06-11 01:51:40,461 INFO: 应用启动 - PID: 9952 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 01:51:50,180 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 27459.28, 明细: 淀粉(15.0公斤)、花生米(50.0公斤)、玉米粒(524.0公斤)、豌豆(30.0公斤)、大米(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 01:51:50,181 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 361348.0, 明细: 香肠(6.0公斤)、鸡肉(192.0公斤)、牛排(530.0公斤)、鸡胸肉(818.0公斤)、猪肉(42.0公斤)、五花肉(500.0公斤)、鸡翅(4.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 01:51:50,182 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 20595.0, 明细: 葱(8.0公斤)、洋葱(6.0公斤)、西兰花(386.0公斤)、茄子(3.0公斤)、南瓜(194.0公斤)、莴笋(2.0公斤)、彩椒(6.0公斤)、芹菜(6.0公斤)、白菜(0.5公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 01:51:50,195 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611003, 入库单=RK20250529123839, 供应商=调味品产品公司, 总金额=409402.28 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1565]
2025-06-11 02:21:50,593 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 25000.0, 明细: 五花肉(500.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 02:21:50,595 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 5000.0, 明细: 白菜(1000.0公斤) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 02:21:50,607 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611004, 入库单=RK20250527155522, 供应商=优质肉类有限公司, 总金额=30000.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1565]
2025-06-11 02:22:38,886 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 335.0, 明细: 猪肉(10.0千克)、牛肉(15.0千克)、鸡肉(20.0千克) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1512]
2025-06-11 02:22:38,893 INFO: 财务凭证生成成功: 凭证号=CYQSYZXPZ20250611005, 入库单=****************, 供应商=调味品产品公司, 总金额=335.00 [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1565]
2025-06-11 02:44:04,493 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250611001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 11, 2, 44, 4), datetime.datetime(2025, 6, 11, 2, 44, 4))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\payables.py:275]
