2025-06-13 01:42:06,311 INFO: 应用启动 - PID: 3308 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-13 01:42:24,048 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-13 01:42:24,048 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-13 01:42:24,048 INFO: 主表数据补全完成，准备保存: 总菜品数=1, 已补全=1, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-13 01:42:24,048 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-13 01:42:24,062 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-13 01:42:24,075 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-13 01:42:24,075 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-13 01:44:28,396 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-13 01:44:28,396 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 剁椒笋片, ID=389 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-13 01:44:28,396 INFO: 副表数据映射构建完成: 1 天, 1 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-13 01:44:28,396 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-13 01:44:28,396 INFO: 主表数据补全完成，准备保存: 总菜品数=12, 已补全=12, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-13 01:44:28,412 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-13 01:44:28,413 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-13 01:44:28,413 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-13 01:44:28,446 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-13 01:44:28,448 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-13 01:46:25,845 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 01:46:25,865 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 04:02:20,355 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-13 04:02:20,356 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-13 04:02:20,358 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-13 04:02:20,358 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-13 04:02:20,974 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-13 06:53:36,958 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:53:36,968 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:54:57,548 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:54:57,555 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:56:11,300 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:56:11,317 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:56:30,947 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:56:30,954 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:56:48,351 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:56:48,376 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:57:49,410 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:57:49,418 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:57:51,489 INFO: 生成固定二维码 - 用户: guest_demo, 学校: 海淀区中关村第一小学 (ID: 44) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-13 06:57:51,491 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-13 06:57:51,583 INFO: 成功生成二维码base64，数据长度: 1236 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-13 06:57:51,594 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-13 06:57:51,614 INFO: 成功生成二维码base64，数据长度: 1084 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-13 06:58:06,030 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:58:06,044 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:58:15,055 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:58:15,071 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:59:08,579 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:59:08,587 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 06:59:10,886 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 06:59:10,893 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:01:30,501 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:01:30,517 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:03:50,847 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:03:50,851 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:04:10,332 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:04:10,342 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:04:13,051 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:04:13,062 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:04:16,458 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:04:16,490 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:04:17,387 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:04:17,393 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:04:18,439 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:04:18,457 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:05:53,570 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:05:53,575 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 07:06:47,118 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 07:06:47,169 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 08:48:14,289 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 08:48:14,339 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 08:55:55,081 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 08:55:55,090 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 12:50:59,832 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 12:50:59,841 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 15:01:15,312 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 15:01:15,327 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 15:01:38,308 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 15:01:38,312 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 17:16:57,183 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 17:16:57,190 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 18:16:50,784 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 18:16:50,813 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 18:28:11,884 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 18:28:11,954 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 18:38:36,317 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 18:38:36,375 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 20:22:01,119 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 20:22:01,194 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 20:35:18,945 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 20:35:18,998 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 20:36:21,684 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-13 20:36:21,698 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-13 20:47:18,923 ERROR: 确认订单失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'confirmed_at' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            UPDATE purchase_orders
            SET status = ?, confirmed_at = ?
            WHERE id = ?
        ]
[parameters: ('已确认', '2025-06-13 20:47:18', 61)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\StudentsCMSSP\app\routes\purchase_order.py:1530]
2025-06-13 20:47:24,742 ERROR: 标记订单准备入库失败: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'delivered_at' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'delivery_notes' 无效。 (207); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            UPDATE purchase_orders
            SET status = ?, delivered_at = ?, delivery_notes = ?
            WHERE id = ?
        ]
[parameters: ('准备入库', '2025-06-13 20:47:24', '', 61)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\StudentsCMSSP\app\routes\purchase_order.py:1639]
