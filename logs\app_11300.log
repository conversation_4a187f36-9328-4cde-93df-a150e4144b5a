2025-06-11 14:33:34,249 INFO: 应用启动 - PID: 11300 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 14:34:20,291 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 14:34:20,311 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 14:34:45,273 ERROR: 获取资产负债表数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            WITH subject_balances AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id,
                    ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) as balance
                FROM accounting_subjects s
                LEFT JOIN voucher_details vd ON s.id = vd.subject_id
                LEFT JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE s.area_id = ?
                AND s.is_active = 1
                AND (fv.voucher_date IS NULL OR fv.voucher_date <= ?)
                AND (fv.status IS NULL OR fv.status IN ('已审核', '已记账'))
                GROUP BY s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id
                HAVING ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) != 0
            )
            SELECT * FROM subject_balances
            WHERE subject_type IN ('资产', '负债', '所有者权益')
            ORDER BY subject_type, code
        ]
[parameters: (42, datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:263]
2025-06-11 14:34:56,874 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:390]
2025-06-11 14:36:21,799 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:390]
2025-06-11 14:38:22,326 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:390]
2025-06-11 14:38:39,229 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:390]
2025-06-11 14:41:21,983 ERROR: 获取成本分析数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = ?
                AND si.stock_in_date >= ?
                AND si.stock_in_date <= ?
                AND si.status = '已确认'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            ]
[parameters: (42, datetime.date(2025, 6, 1), datetime.date(2025, 6, 11))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\reports.py:390]
2025-06-11 14:41:29,473 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 14:41:29,493 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
