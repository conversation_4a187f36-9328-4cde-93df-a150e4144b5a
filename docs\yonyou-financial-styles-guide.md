# 用友财务系统样式使用指南

## 🎯 设计理念

本样式系统遵循用友财务软件的专业设计标准，追求：
- **专业性**：符合财务人员的使用习惯
- **统一性**：所有财务模块保持一致的视觉风格
- **优雅性**：简洁而不失美观，避免花里胡哨
- **实用性**：提高财务工作效率

## 📋 核心样式类别

### 1. 财务金额显示

```html
<!-- 标准金额显示 -->
<span class="uf-financial-amount">¥68,000.00</span>

<!-- 正数金额 -->
<span class="uf-financial-amount positive">¥68,000.00</span>

<!-- 负数金额 -->
<span class="uf-financial-amount negative">¥-5,000.00</span>

<!-- 零金额 -->
<span class="uf-financial-amount zero">¥0.00</span>
```

### 2. 财务金额输入

```html
<!-- 金额输入框 -->
<input type="text" class="uf-amount-input" placeholder="0.00" value="68000.00">
```

### 3. 财务科目显示

```html
<!-- 科目代码 -->
<span class="uf-subject-code">1201</span>

<!-- 科目名称 -->
<span class="uf-subject-name">原材料</span>

<!-- 组合显示 -->
<span class="uf-subject-code">1201</span> 
<span class="uf-subject-name">原材料</span>
```

### 4. 财务凭证号

```html
<!-- 凭证号显示 -->
<span class="uf-voucher-number">PZ20250611001</span>
```

### 5. 财务状态标签

```html
<!-- 草稿状态 -->
<span class="uf-financial-status draft">草稿</span>

<!-- 已审核状态 -->
<span class="uf-financial-status approved">已审核</span>

<!-- 已记账状态 -->
<span class="uf-financial-status posted">已记账</span>

<!-- 已取消状态 -->
<span class="uf-financial-status cancelled">已取消</span>
```

### 6. 财务专用按钮

```html
<!-- 生成按钮 -->
<button class="uf-financial-btn generate">
    <i class="fas fa-plus uf-financial-icon"></i>生成应付账款
</button>

<!-- 审核按钮 -->
<button class="uf-financial-btn approve">
    <i class="fas fa-check uf-financial-icon"></i>审核
</button>

<!-- 取消按钮 -->
<button class="uf-financial-btn cancel">
    <i class="fas fa-times uf-financial-icon"></i>取消
</button>
```

### 7. 财务专用表格

```html
<table class="uf-financial-table">
    <thead>
        <tr>
            <th class="col-date">日期</th>
            <th class="col-voucher">凭证号</th>
            <th class="col-subject-code">科目代码</th>
            <th class="col-subject-name">科目名称</th>
            <th class="col-amount">金额</th>
            <th class="col-summary">摘要</th>
            <th class="col-status">状态</th>
            <th class="col-actions">操作</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="col-date">
                <span class="uf-financial-date">2025-06-11</span>
            </td>
            <td class="col-voucher">
                <span class="uf-voucher-number">PZ20250611001</span>
            </td>
            <td class="col-subject-code">
                <span class="uf-subject-code">1201</span>
            </td>
            <td class="col-subject-name">
                <span class="uf-subject-name">原材料</span>
            </td>
            <td class="col-amount">
                <span class="uf-financial-amount">¥68,000.00</span>
            </td>
            <td class="col-summary">
                <span class="uf-financial-summary">采购食材原料</span>
            </td>
            <td class="col-status">
                <span class="uf-financial-status approved">已审核</span>
            </td>
            <td class="col-actions">
                <button class="uf-financial-btn">查看</button>
            </td>
        </tr>
    </tbody>
</table>
```

### 8. 财务表单

```html
<div class="uf-financial-form">
    <div class="uf-financial-form-row">
        <div class="uf-financial-form-group">
            <label class="uf-financial-form-label">凭证日期：</label>
            <input type="date" class="uf-financial-form-control" value="2025-06-11">
        </div>
        <div class="uf-financial-form-group">
            <label class="uf-financial-form-label">凭证类型：</label>
            <select class="uf-financial-form-control">
                <option>入库凭证</option>
                <option>付款凭证</option>
            </select>
        </div>
    </div>
    <div class="uf-financial-form-row">
        <div class="uf-financial-form-group">
            <label class="uf-financial-form-label">金额：</label>
            <input type="text" class="uf-amount-input" placeholder="0.00">
        </div>
    </div>
</div>
```

### 9. 财务汇总卡片

```html
<div class="uf-financial-summary-card">
    <div class="uf-financial-summary-title">
        <i class="fas fa-chart-bar uf-financial-icon"></i>财务汇总
    </div>
    <div class="uf-financial-summary-content">
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">应付账款总额</div>
            <div class="uf-financial-summary-value">¥156,800.00</div>
        </div>
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">已付金额</div>
            <div class="uf-financial-summary-value">¥88,800.00</div>
        </div>
        <div class="uf-financial-summary-item">
            <div class="uf-financial-summary-label">未付余额</div>
            <div class="uf-financial-summary-value">¥68,000.00</div>
        </div>
    </div>
</div>
```

### 10. 财务工具栏

```html
<div class="uf-financial-toolbar">
    <div class="uf-financial-toolbar-left">
        <span class="uf-financial-toolbar-title">应付账款管理</span>
    </div>
    <div class="uf-financial-toolbar-right">
        <button class="uf-financial-btn generate">
            <i class="fas fa-plus uf-financial-icon"></i>生成应付账款
        </button>
        <button class="uf-financial-btn">
            <i class="fas fa-download uf-financial-icon"></i>导出
        </button>
    </div>
</div>
```

## 🎨 样式特点

### 字体规范
- **主字体**：Microsoft YaHei, SimSun, 宋体, Arial, sans-serif
- **金额字体**：Courier New, Times New Roman, monospace
- **标准字号**：12px（保持专业财务软件的传统）

### 颜色规范
- **主色调**：#0066cc（用友经典蓝）
- **成功色**：#1e7e34（深绿）
- **警告色**：#e0a800（金黄）
- **危险色**：#bd2130（深红）
- **边框色**：#c0c0c0（浅灰）

### 尺寸规范
- **按钮高度**：24px（财务专用）
- **表格行高**：24px
- **表格表头高度**：28px
- **边框圆角**：1px（保持方正专业感）

## 💡 使用建议

1. **金额显示**：统一使用 `uf-financial-amount` 类
2. **按钮操作**：使用 `uf-financial-btn` 及其变体
3. **表格展示**：使用 `uf-financial-table` 及列宽类
4. **表单输入**：使用 `uf-financial-form` 系列类
5. **状态显示**：使用 `uf-financial-status` 及其变体

## 🔧 响应式支持

样式系统包含完整的响应式设计，在移动设备上自动调整：
- 表单布局变为垂直排列
- 工具栏按钮重新排列
- 字体和间距适配小屏幕

这套样式系统确保了财务模块的专业性、统一性和优雅性，为用户提供了类似用友财务软件的专业体验。
