<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .qr-test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .qr-display {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .qr-display img {
            max-width: 300px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
        }
        
        .url-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 15px 0;
        }
        
        .test-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qr-test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-qrcode"></i> 二维码测试页面
            </h1>
            
            <div class="test-info">
                <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
                <p>此页面用于测试食品留样标签的二维码生成和扫描功能。</p>
                <p><strong>测试菜品：</strong>酥炸鸡柳</p>
                <p><strong>测试日期：</strong>2025-01-15</p>
                <p><strong>测试餐次：</strong>午餐</p>
                <p><strong>测试区域：</strong>系统区域</p>
            </div>
            
            <div class="qr-display">
                <h5>生成的二维码</h5>
                {% if qr_code %}
                <img src="data:image/png;base64,{{ qr_code }}" alt="测试二维码">
                {% else %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 二维码生成失败
                </div>
                {% endif %}
            </div>
            
            <div class="url-display">
                <strong>二维码包含的URL：</strong><br>
                {{ test_url }}
            </div>
            
            <div class="test-steps">
                <h5><i class="fas fa-clipboard-list"></i> 测试步骤</h5>
                <ol>
                    <li>使用手机扫码软件扫描上方二维码</li>
                    <li>检查是否能正确识别URL</li>
                    <li>点击URL或在浏览器中打开</li>
                    <li>验证是否能正常显示菜品溯源信息</li>
                </ol>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <a href="{{ test_url }}" class="btn btn-primary btn-block" target="_blank">
                        <i class="fas fa-external-link-alt"></i> 直接访问URL
                    </a>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary btn-block" onclick="window.print()">
                        <i class="fas fa-print"></i> 打印测试
                    </button>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>可能的问题和解决方案</h5>
                <ul>
                    <li><strong>扫码失败：</strong>检查二维码清晰度，尝试调整扫码距离</li>
                    <li><strong>URL无法访问：</strong>检查网络连接和域名解析</li>
                    <li><strong>中文乱码：</strong>已优化URL编码处理</li>
                    <li><strong>打印模糊：</strong>建议使用高分辨率打印机</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
