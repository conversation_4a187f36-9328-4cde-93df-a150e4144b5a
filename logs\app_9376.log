2025-06-14 21:09:41,081 INFO: 应用启动 - PID: 9376 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-14 21:25:08,355 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 21:25:20,959 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-14 21:25:20,959 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-14 21:25:20,960 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-14 21:25:20,967 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-14 21:25:20,967 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-14 21:25:20,973 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-14 21:25:20,974 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,974 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,974 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,975 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,975 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,975 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,976 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,976 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,976 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,976 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,976 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,977 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,978 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,978 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,978 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,978 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,979 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,979 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,979 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,979 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,980 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,980 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,980 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,980 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,980 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,981 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,981 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,981 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,981 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,981 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,982 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,982 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,982 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,982 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,982 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,983 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,984 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,984 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,984 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,984 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,984 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,985 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,985 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,985 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,985 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,986 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,986 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-14 21:25:20,991 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-14 21:25:20,991 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-14 21:25:20,992 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-14 21:25:20,996 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-14 21:25:21,010 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-14 21:25:21,013 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,015 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,015 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,017 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,017 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,018 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,020 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,021 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,022 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,023 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,024 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,025 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,026 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,027 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,029 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,030 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,031 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,032 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,034 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,035 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,036 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-14 21:25:21,036 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
