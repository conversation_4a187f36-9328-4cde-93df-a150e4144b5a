<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- URL重写规则 - 反向代理到Flask应用 -->
        <rewrite>
            <rules>
                <!-- 重定向www到非www域名 -->
                <rule name="RedirectWWWToNonWWW" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions>
                        <add input="{HTTP_HOST}" pattern="^www\.xiaoyuanst\.com$" ignoreCase="true" />
                    </conditions>
                    <action type="Redirect" url="http://xiaoyuanst.com/{R:1}" redirectType="Permanent" />
                </rule>

                <!-- 反向代理到Flask应用 -->
                <rule name="ReverseProxyInboundRule1" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
                    <serverVariables>
                        <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                        <set name="HTTP_X_FORWARDED_PROTO" value="http" />
                        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
                    </serverVariables>
                </rule>
            </rules>
        </rewrite>

        <!-- 默认文档设置 -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.html" />
            </files>
        </defaultDocument>

        <!-- HTTP错误页面 -->
        <httpErrors errorMode="Detailed" />

        <!-- 安全头设置 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>
    </system.webServer>
</configuration>
